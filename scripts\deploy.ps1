# Demo IA Server - PowerShell Deployment Script
# Uso: .\scripts\deploy.ps1 [-Environment production] [-Version latest]

param(
    [string]$Environment = "production",
    [string]$Version = "latest",
    [switch]$Help
)

# Configuración
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectDir = Split-Path -Parent $ScriptDir
$BackupDir = "C:\backup\demoai"
$LogFile = "C:\logs\demoai\deploy.log"

# Crear directorio de logs si no existe
$LogDir = Split-Path -Parent $LogFile
if (!(Test-Path $LogDir)) {
    New-Item -ItemType Directory -Path $LogDir -Force | Out-Null
}

# Funciones de logging
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogMessage = "[$Timestamp] [$Level] $Message"
    
    switch ($Level) {
        "ERROR" { Write-Host $LogMessage -ForegroundColor Red }
        "WARN"  { Write-Host $LogMessage -ForegroundColor Yellow }
        "INFO"  { Write-Host $LogMessage -ForegroundColor Green }
        "DEBUG" { Write-Host $LogMessage -ForegroundColor Blue }
        default { Write-Host $LogMessage }
    }
    
    Add-Content -Path $LogFile -Value $LogMessage
}

function Write-Error-Log {
    param([string]$Message)
    Write-Log $Message "ERROR"
    exit 1
}

function Write-Warning-Log {
    param([string]$Message)
    Write-Log $Message "WARN"
}

# Mostrar ayuda
if ($Help) {
    Write-Host "Demo IA Server - PowerShell Deployment Script"
    Write-Host ""
    Write-Host "Uso: .\scripts\deploy.ps1 [-Environment <env>] [-Version <ver>]"
    Write-Host ""
    Write-Host "Parámetros:"
    Write-Host "  -Environment  Entorno de deployment (production, staging, development)"
    Write-Host "  -Version      Versión a desplegar (default: latest)"
    Write-Host "  -Help         Mostrar esta ayuda"
    Write-Host ""
    Write-Host "Ejemplos:"
    Write-Host "  .\scripts\deploy.ps1 -Environment production -Version v1.0.0"
    Write-Host "  .\scripts\deploy.ps1 -Environment staging"
    exit 0
}

# Verificar prerrequisitos
function Test-Prerequisites {
    Write-Log "Verificando prerrequisitos..."
    
    # Verificar Docker
    try {
        $dockerVersion = docker --version
        Write-Log "Docker encontrado: $dockerVersion"
    }
    catch {
        Write-Error-Log "Docker no está instalado o no está en el PATH"
    }
    
    # Verificar Docker Compose
    try {
        $composeVersion = docker-compose --version
        Write-Log "Docker Compose encontrado: $composeVersion"
    }
    catch {
        Write-Error-Log "Docker Compose no está instalado o no está en el PATH"
    }
    
    # Verificar archivo de entorno
    $envFile = Join-Path $ProjectDir ".env.$Environment"
    if (!(Test-Path $envFile)) {
        Write-Error-Log "Archivo .env.$Environment no encontrado en $envFile"
    }
    
    Write-Log "Prerrequisitos verificados ✓"
}

# Crear backup
function New-Backup {
    Write-Log "Creando backup..."
    
    if (!(Test-Path $BackupDir)) {
        New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
    }
    
    $BackupFile = Join-Path $BackupDir "backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').zip"
    
    # Archivos a respaldar
    $FilesToBackup = @(
        ".env.*",
        "docker-compose.yml",
        "nginx\*",
        "logs\*"
    )
    
    try {
        $TempDir = Join-Path $env:TEMP "demoai_backup"
        if (Test-Path $TempDir) {
            Remove-Item $TempDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $TempDir -Force | Out-Null
        
        foreach ($Pattern in $FilesToBackup) {
            $Files = Get-ChildItem -Path $ProjectDir -Filter $Pattern -Recurse -ErrorAction SilentlyContinue
            foreach ($File in $Files) {
                $RelativePath = $File.FullName.Substring($ProjectDir.Length + 1)
                $DestPath = Join-Path $TempDir $RelativePath
                $DestDir = Split-Path -Parent $DestPath
                
                if (!(Test-Path $DestDir)) {
                    New-Item -ItemType Directory -Path $DestDir -Force | Out-Null
                }
                
                Copy-Item $File.FullName $DestPath -Force
            }
        }
        
        Compress-Archive -Path "$TempDir\*" -DestinationPath $BackupFile -Force
        Remove-Item $TempDir -Recurse -Force
        
        Write-Log "Backup creado: $BackupFile"
    }
    catch {
        Write-Warning-Log "Error creando backup: $($_.Exception.Message)"
    }
}

# Verificaciones pre-deployment
function Test-PreDeployment {
    Write-Log "Ejecutando verificaciones pre-deployment..."
    
    # Verificar conectividad a servicios externos
    Write-Log "Verificando conectividad a Supabase..." "DEBUG"
    try {
        $response = Invoke-WebRequest -Uri "https://supabase.com" -TimeoutSec 10 -UseBasicParsing
        Write-Log "Conectividad a Supabase: OK"
    }
    catch {
        Write-Warning-Log "No se puede conectar a Supabase"
    }
    
    Write-Log "Verificando conectividad a OpenAI..." "DEBUG"
    try {
        $response = Invoke-WebRequest -Uri "https://api.openai.com" -TimeoutSec 10 -UseBasicParsing
        Write-Log "Conectividad a OpenAI: OK"
    }
    catch {
        Write-Warning-Log "No se puede conectar a OpenAI API"
    }
    
    # Verificar espacio en disco
    $Drive = (Get-Location).Drive
    $DiskInfo = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='$($Drive.Name)'"
    $FreeSpaceGB = [math]::Round($DiskInfo.FreeSpace / 1GB, 2)
    $TotalSpaceGB = [math]::Round($DiskInfo.Size / 1GB, 2)
    $UsagePercent = [math]::Round((($TotalSpaceGB - $FreeSpaceGB) / $TotalSpaceGB) * 100, 1)
    
    Write-Log "Espacio en disco: $FreeSpaceGB GB libres de $TotalSpaceGB GB (${UsagePercent}% usado)"
    
    if ($UsagePercent -gt 80) {
        Write-Warning-Log "Uso de disco alto: ${UsagePercent}%"
    }
    
    Write-Log "Verificaciones pre-deployment completadas"
}

# Build de la aplicación
function Build-Application {
    Write-Log "Construyendo aplicación..."
    
    Set-Location $ProjectDir
    
    try {
        # Pull de imágenes base
        Write-Log "Descargando imágenes base..." "DEBUG"
        docker-compose pull nginx redis prometheus grafana 2>$null
        
        # Build de la aplicación
        Write-Log "Construyendo imagen de la aplicación..." "DEBUG"
        docker-compose build --no-cache demo-ia-server
        
        # Tag de la imagen con versión
        $ImageId = docker-compose images -q demo-ia-server
        docker tag $ImageId "demo-ia-server:$Version"
        
        Write-Log "Aplicación construida exitosamente"
    }
    catch {
        Write-Error-Log "Error construyendo aplicación: $($_.Exception.Message)"
    }
}

# Deploy de la aplicación
function Deploy-Application {
    Write-Log "Desplegando aplicación..."
    
    Set-Location $ProjectDir
    
    try {
        # Copiar archivo de entorno
        $SourceEnv = ".env.$Environment"
        $TargetEnv = ".env.production"
        Copy-Item $SourceEnv $TargetEnv -Force
        
        # Detener servicios existentes
        Write-Log "Deteniendo servicios existentes..." "DEBUG"
        docker-compose down --remove-orphans
        
        # Iniciar servicios
        if ($Environment -eq "production") {
            Write-Log "Iniciando servicios de producción..." "DEBUG"
            docker-compose up -d demo-ia-server nginx
        }
        else {
            Write-Log "Iniciando todos los servicios..." "DEBUG"
            docker-compose --profile monitoring --profile cache up -d
        }
        
        Write-Log "Aplicación desplegada"
    }
    catch {
        Write-Error-Log "Error desplegando aplicación: $($_.Exception.Message)"
    }
}

# Health checks post-deployment
function Test-PostDeployment {
    Write-Log "Ejecutando verificaciones post-deployment..."
    
    # Esperar a que la aplicación esté lista
    Write-Log "Esperando a que la aplicación esté lista..." "DEBUG"
    Start-Sleep -Seconds 30
    
    # Verificar que los contenedores estén corriendo
    $RunningContainers = docker-compose ps --filter "status=running"
    if (!$RunningContainers) {
        Write-Error-Log "Ningún contenedor está corriendo"
    }
    
    # Health check de la aplicación
    Write-Log "Verificando health check..." "DEBUG"
    $HealthCheckPassed = $false
    
    for ($i = 1; $i -le 10; $i++) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3000/health" -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Log "Health check exitoso ✓"
                $HealthCheckPassed = $true
                break
            }
        }
        catch {
            if ($i -eq 10) {
                Write-Error-Log "Health check falló después de 10 intentos"
            }
            Start-Sleep -Seconds 5
        }
    }
    
    if ($HealthCheckPassed) {
        # Verificar endpoints principales
        Write-Log "Verificando endpoints principales..." "DEBUG"
        
        # Test webhook endpoint (debe retornar 401 sin autenticación)
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3000/webhook/news" -Method POST -UseBasicParsing -ErrorAction SilentlyContinue
        }
        catch {
            if ($_.Exception.Response.StatusCode -eq 401) {
                Write-Log "Webhook endpoint funcionando ✓"
            }
            else {
                Write-Warning-Log "Webhook endpoint puede tener problemas"
            }
        }
        
        # Test metrics endpoint
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3000/webhook/metrics" -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Log "Metrics endpoint funcionando ✓"
            }
        }
        catch {
            Write-Warning-Log "Metrics endpoint puede tener problemas"
        }
    }
    
    Write-Log "Verificaciones post-deployment completadas"
}

# Cleanup de recursos antiguos
function Invoke-Cleanup {
    Write-Log "Limpiando recursos antiguos..."
    
    try {
        # Limpiar imágenes no utilizadas
        docker image prune -f | Out-Null
        
        # Limpiar volúmenes no utilizados
        docker volume prune -f | Out-Null
        
        # Limpiar backups antiguos (>30 días)
        if (Test-Path $BackupDir) {
            $OldBackups = Get-ChildItem -Path $BackupDir -Filter "backup_*.zip" | Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-30) }
            foreach ($Backup in $OldBackups) {
                Remove-Item $Backup.FullName -Force
                Write-Log "Backup antiguo eliminado: $($Backup.Name)" "DEBUG"
            }
        }
        
        Write-Log "Cleanup completado"
    }
    catch {
        Write-Warning-Log "Error durante cleanup: $($_.Exception.Message)"
    }
}

# Función principal
function Main {
    Write-Log "=== Iniciando deployment de Demo IA Server ==="
    Write-Log "Entorno: $Environment"
    Write-Log "Versión: $Version"
    
    try {
        Test-Prerequisites
        New-Backup
        Test-PreDeployment
        Build-Application
        Deploy-Application
        Test-PostDeployment
        Invoke-Cleanup
        
        Write-Log "=== Deployment completado exitosamente ==="
        
        # Mostrar información útil
        Write-Log "Información del deployment:" "INFO"
        Write-Log "- Aplicación: http://localhost:3000" "INFO"
        Write-Log "- Health check: http://localhost:3000/health" "INFO"
        Write-Log "- Métricas: http://localhost:3000/webhook/metrics" "INFO"
        Write-Log "- Logs: docker-compose logs -f demo-ia-server" "INFO"
        
        $GrafanaRunning = docker-compose ps grafana 2>$null | Select-String "Up"
        if ($GrafanaRunning) {
            Write-Log "- Grafana: http://localhost:3001 (admin/admin123)" "INFO"
        }
        
        $PrometheusRunning = docker-compose ps prometheus 2>$null | Select-String "Up"
        if ($PrometheusRunning) {
            Write-Log "- Prometheus: http://localhost:9090" "INFO"
        }
    }
    catch {
        Write-Error-Log "Deployment falló: $($_.Exception.Message)"
    }
}

# Ejecutar deployment
Main
