const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const config = require('./src/config/environment');
const { logger, logError } = require('./src/utils/logger');
const { createApiResponse } = require('./src/models/newsSchema');

// Importar servicios
const monitoringService = require('./src/services/monitoringService');
const openaiService = require('./src/services/openaiService');
const processingService = require('./src/services/processingService');

// Importar rutas
const webhookRoutes = require('./src/routes/webhook');
const healthRoutes = require('./src/routes/health');

// Crear aplicación Express
const app = express();

// Configuración de seguridad con Helmet
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Configuración de CORS
app.use(cors({
  origin: config.webhook.allowedOrigins,
  methods: ['GET', 'POST'],
  allowedHeaders: ['Content-Type', 'x-webhook-signature', 'Authorization'],
  credentials: false
}));

// Middleware para parsear JSON
app.use(express.json({
  limit: '10mb',
  verify: (req, res, buf) => {
    // Guardar el raw body para validación de firma
    req.rawBody = buf;
  }
}));

// Middleware para parsear URL encoded
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Middleware para logging de requests
app.use((req, res, next) => {
  logger.info('Request recibido', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length')
  });
  next();
});

// Configurar trust proxy para obtener IP real
app.set('trust proxy', true);

// Ruta raíz
app.get('/', (req, res) => {
  res.status(200).json(
    createApiResponse(
      true,
      'Demo IA Server - Sistema de Procesamiento de Noticias',
      {
        version: '1.0.0',
        environment: config.server.nodeEnv,
        endpoints: {
          webhook: '/webhook/news',
          health: '/health',
          ping: '/ping',
          ready: '/ready'
        },
        documentation: 'https://github.com/tu-usuario/demo-ia-server'
      }
    )
  );
});

// Rutas principales
app.use('/webhook', webhookRoutes);
app.use('/', healthRoutes);

// Middleware para rutas no encontradas
app.use('*', (req, res) => {
  logger.warn('Ruta no encontrada', {
    method: req.method,
    url: req.url,
    ip: req.ip
  });

  res.status(404).json(
    createApiResponse(
      false,
      'Endpoint no encontrado',
      null,
      {
        code: 'ENDPOINT_NOT_FOUND',
        details: `${req.method} ${req.url} no existe`
      }
    )
  );
});

// Middleware global de manejo de errores
app.use((error, req, res, next) => {
  logError(error, {
    context: 'global_error_handler',
    method: req.method,
    url: req.url,
    ip: req.ip,
    body: req.body
  });

  // Error de JSON malformado
  if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
    return res.status(400).json(
      createApiResponse(
        false,
        'JSON malformado',
        null,
        {
          code: 'INVALID_JSON',
          details: 'El cuerpo de la request contiene JSON inválido'
        }
      )
    );
  }

  // Error genérico
  res.status(500).json(
    createApiResponse(
      false,
      'Error interno del servidor',
      null,
      {
        code: 'INTERNAL_SERVER_ERROR',
        details: config.server.nodeEnv === 'development' ? error.message : 'Error inesperado'
      }
    )
  );
});

// Función para iniciar el servidor
const startServer = () => {
  const port = config.server.port;

  // Inicializar servicios
  monitoringService.initialize();
  openaiService.initialize();
  processingService.initialize();

  const server = app.listen(port, () => {
    logger.info(`Servidor iniciado correctamente`, {
      port,
      environment: config.server.nodeEnv,
      timestamp: new Date().toISOString()
    });

    console.log(`🚀 Demo IA Server ejecutándose en puerto ${port}`);
    console.log(`📝 Logs: ${config.logging.filePath}`);
    console.log(`🔗 Health Check: http://localhost:${port}/health`);
    console.log(`📡 Webhook Endpoint: http://localhost:${port}/webhook/news`);
    console.log(`📊 Metrics: http://localhost:${port}/webhook/metrics`);
  });

  // Manejo graceful de cierre del servidor
  process.on('SIGTERM', () => {
    logger.info('SIGTERM recibido, cerrando servidor...');
    server.close(() => {
      logger.info('Servidor cerrado correctamente');
      process.exit(0);
    });
  });

  process.on('SIGINT', () => {
    logger.info('SIGINT recibido, cerrando servidor...');
    server.close(() => {
      logger.info('Servidor cerrado correctamente');
      process.exit(0);
    });
  });

  return server;
};

// Iniciar servidor solo si este archivo es ejecutado directamente
if (require.main === module) {
  startServer();
}

module.exports = app;
