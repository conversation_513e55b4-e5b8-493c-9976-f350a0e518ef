// Servicio de gestión de prompts personalizados
const { getSupabaseClient } = require('../config/database');
const { logger, logError } = require('../utils/logger');

class PromptService {
  constructor() {
    this.client = null;
    this.promptCache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutos
  }

  initialize(useServiceRole = true) {
    try {
      this.client = getSupabaseClient(useServiceRole);
      return true;
    } catch (error) {
      logError(error, { context: 'PromptService.initialize' });
      return false;
    }
  }

  // Obtener prompt personalizado para un medio
  async getPromptForMedio(medioOrigen) {
    try {
      if (!this.client) this.initialize();

      // Verificar cache primero
      const cacheKey = `prompt_${medioOrigen}`;
      const cached = this.promptCache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        logger.info('Prompt obtenido desde cache', { medio: medioOrigen });
        return { success: true, data: cached.data };
      }

      logger.info('Obteniendo prompt desde base de datos', { medio: medioOrigen });

      // Buscar prompt específico del medio
      const { data, error } = await this.client
        .from('prompts_medios')
        .select('*')
        .eq('medio_origen', medioOrigen)
        .eq('activo', true)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
        throw error;
      }

      let promptData;

      if (data) {
        // Prompt específico encontrado
        promptData = {
          template: data.template_prompt,
          config: {
            style: data.estilo,
            tone: data.tono,
            audience: data.audiencia,
            maxWords: data.max_palabras,
            format: data.formato_salida
          },
          isCustom: true,
          medio: medioOrigen
        };
      } else {
        // Usar prompt por defecto
        promptData = this.getDefaultPrompt(medioOrigen);
      }

      // Guardar en cache
      this.promptCache.set(cacheKey, {
        data: promptData,
        timestamp: Date.now()
      });

      logger.info('Prompt obtenido exitosamente', {
        medio: medioOrigen,
        isCustom: promptData.isCustom,
        hasTemplate: !!promptData.template
      });

      return { success: true, data: promptData };

    } catch (error) {
      logError(error, {
        context: 'PromptService.getPromptForMedio',
        medio: medioOrigen
      });

      // Fallback a prompt por defecto en caso de error
      return {
        success: true,
        data: this.getDefaultPrompt(medioOrigen),
        warning: 'Usando prompt por defecto debido a error en BD'
      };
    }
  }

  // Obtener prompt por defecto
  getDefaultPrompt(medioOrigen) {
    const defaultTemplate = `
Reescribe la siguiente noticia adaptándola al estilo del medio "{medio}":

NOTICIA ORIGINAL:
Título: {title}
Resumen: {summary}
Contenido: {content}

INSTRUCCIONES:
- Mantén la información factual exacta y verificable
- Adapta el tono y estilo al medio especificado
- Conserva la estructura: título, resumen, contenido
- Máximo {maxWords} palabras en el contenido
- Usa un lenguaje claro y profesional
- Mantén la objetividad periodística

FORMATO DE RESPUESTA:
TÍTULO: [nuevo título adaptado al estilo del medio]
RESUMEN: [nuevo resumen de máximo 2 líneas]
CONTENIDO: [nuevo contenido adaptado, bien estructurado en párrafos]

ESTILO ESPECÍFICO: {style}
TONO: {tone}
AUDIENCIA: {audience}
    `.trim();

    return {
      template: defaultTemplate,
      config: {
        style: 'profesional y objetivo',
        tone: 'informativo',
        audience: 'público general',
        maxWords: '500',
        format: 'título, resumen, contenido'
      },
      isCustom: false,
      medio: medioOrigen
    };
  }

  // Crear o actualizar prompt para un medio
  async savePromptForMedio(medioOrigen, promptData) {
    try {
      if (!this.client) this.initialize();

      logger.info('Guardando prompt personalizado', {
        medio: medioOrigen,
        hasTemplate: !!promptData.template
      });

      const dataToSave = {
        medio_origen: medioOrigen,
        template_prompt: promptData.template,
        estilo: promptData.config?.style || 'profesional',
        tono: promptData.config?.tone || 'informativo',
        audiencia: promptData.config?.audience || 'público general',
        max_palabras: promptData.config?.maxWords || 500,
        formato_salida: promptData.config?.format || 'título, resumen, contenido',
        activo: true,
        updated_at: new Date().toISOString()
      };

      // Intentar actualizar primero
      const { data: updateData, error: updateError } = await this.client
        .from('prompts_medios')
        .update(dataToSave)
        .eq('medio_origen', medioOrigen)
        .select()
        .single();

      if (updateError && updateError.code === 'PGRST116') {
        // No existe, crear nuevo
        const { data: insertData, error: insertError } = await this.client
          .from('prompts_medios')
          .insert([dataToSave])
          .select()
          .single();

        if (insertError) {
          throw insertError;
        }

        logger.info('Prompt creado exitosamente', {
          medio: medioOrigen,
          id: insertData.id
        });

        // Limpiar cache
        this.promptCache.delete(`prompt_${medioOrigen}`);

        return { success: true, data: insertData, action: 'created' };
      } else if (updateError) {
        throw updateError;
      } else {
        logger.info('Prompt actualizado exitosamente', {
          medio: medioOrigen,
          id: updateData.id
        });

        // Limpiar cache
        this.promptCache.delete(`prompt_${medioOrigen}`);

        return { success: true, data: updateData, action: 'updated' };
      }

    } catch (error) {
      logError(error, {
        context: 'PromptService.savePromptForMedio',
        medio: medioOrigen
      });

      return {
        success: false,
        error: {
          code: 'PROMPT_SAVE_ERROR',
          message: error.message,
          originalError: error
        }
      };
    }
  }

  // Listar todos los prompts activos
  async getAllActivePrompts() {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('prompts_medios')
        .select('*')
        .eq('activo', true)
        .order('medio_origen');

      if (error) {
        throw error;
      }

      logger.info('Prompts activos obtenidos', { count: data.length });

      return { success: true, data };

    } catch (error) {
      logError(error, { context: 'PromptService.getAllActivePrompts' });

      return {
        success: false,
        error: {
          code: 'PROMPTS_FETCH_ERROR',
          message: error.message
        }
      };
    }
  }

  // Desactivar prompt para un medio
  async deactivatePrompt(medioOrigen) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('prompts_medios')
        .update({ activo: false, updated_at: new Date().toISOString() })
        .eq('medio_origen', medioOrigen)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Limpiar cache
      this.promptCache.delete(`prompt_${medioOrigen}`);

      logger.info('Prompt desactivado', { medio: medioOrigen });

      return { success: true, data };

    } catch (error) {
      logError(error, {
        context: 'PromptService.deactivatePrompt',
        medio: medioOrigen
      });

      return {
        success: false,
        error: {
          code: 'PROMPT_DEACTIVATE_ERROR',
          message: error.message
        }
      };
    }
  }

  // Limpiar cache de prompts
  clearCache() {
    this.promptCache.clear();
    logger.info('Cache de prompts limpiado');
  }

  // Obtener estadísticas del cache
  getCacheStats() {
    return {
      size: this.promptCache.size,
      keys: Array.from(this.promptCache.keys()),
      expiryTime: this.cacheExpiry
    };
  }
}

// Exportar instancia singleton
const promptService = new PromptService();

module.exports = promptService;
