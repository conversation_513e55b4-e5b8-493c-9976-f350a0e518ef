-- =====================================================
-- MIGRACIÓN DE NOTICIAS A DIARIOS
-- Ejecutar este script en el SQL Editor de Supabase
-- =====================================================

-- 1. Eliminar tabla noticias si existe (cuidado con datos)
DROP TABLE IF EXISTS news_processing CASCADE;
DROP TABLE IF EXISTS noticias CASCADE;

-- 2. <PERSON>rear tabla diarios con la nueva estructura
CREATE TABLE IF NOT EXISTS diarios (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  medio_origen VARCHAR(50) NOT NULL,
  header VARCHAR(200) NOT NULL,
  title VARCHAR(300) NOT NULL,
  summary TEXT,
  content TEXT NOT NULL,
  images_url TEXT[],
  categories VARCHAR(100)[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Recrear tabla news_processing con referencia a diarios
CREATE TABLE IF NOT EXISTS news_processing (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  diario_id UUID REFERENCES diarios(id) ON DELETE CASCADE,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  openai_tokens_used INTEGER,
  processing_time_ms INTEGER,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- 4. Crear índices para diarios
CREATE INDEX IF NOT EXISTS idx_diarios_medio_origen ON diarios(medio_origen);
CREATE INDEX IF NOT EXISTS idx_diarios_created_at ON diarios(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_diarios_categories ON diarios USING GIN(categories);

-- 5. Crear índices para news_processing
CREATE INDEX IF NOT EXISTS idx_news_processing_diario_id ON news_processing(diario_id);
CREATE INDEX IF NOT EXISTS idx_news_processing_status ON news_processing(status);
CREATE INDEX IF NOT EXISTS idx_news_processing_created_at ON news_processing(created_at DESC);

-- 6. Recrear triggers
DROP TRIGGER IF EXISTS update_diarios_updated_at ON diarios;
CREATE TRIGGER update_diarios_updated_at 
    BEFORE UPDATE ON diarios 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_news_processing_updated_at ON news_processing;
CREATE TRIGGER update_news_processing_updated_at 
    BEFORE UPDATE ON news_processing 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. Configurar RLS
ALTER TABLE diarios ENABLE ROW LEVEL SECURITY;
ALTER TABLE news_processing ENABLE ROW LEVEL SECURITY;

-- 8. Recrear políticas
DROP POLICY IF EXISTS "Service role can do everything on diarios" ON diarios;
CREATE POLICY "Service role can do everything on diarios" ON diarios
  FOR ALL USING (auth.role() = 'service_role');

DROP POLICY IF EXISTS "Service role can do everything on news_processing" ON news_processing;
CREATE POLICY "Service role can do everything on news_processing" ON news_processing
  FOR ALL USING (auth.role() = 'service_role');

-- 9. Verificar tablas creadas
SELECT 
  schemaname,
  tablename,
  tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('diarios', 'medios', 'prompts_medios', 'system_logs', 'news_processing')
ORDER BY tablename;
