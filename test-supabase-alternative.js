// Test alternativo con configuración diferente de Supabase
const { createClient } = require('@supabase/supabase-js');
const config = require('./src/config/environment');

async function testAlternativeClient() {
  console.log('🧪 Test con Cliente Supabase Alternativo\n');
  
  try {
    // Crear cliente con configuración diferente
    console.log('1. 🔧 Creando cliente con configuración alternativa...');
    
    const client = createClient(
      config.supabase.url,
      config.supabase.serviceRoleKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        },
        global: {
          headers: {
            'Authorization': `Bearer ${config.supabase.serviceRoleKey}`
          }
        }
      }
    );
    
    console.log('   ✅ Cliente creado');
    
    // Test 1: Verificar conexión
    console.log('\n2. 🔍 Verificando conexión...');
    const { data: testData, error: testError } = await client
      .from('diarios')
      .select('count', { count: 'exact', head: true });
    
    if (testError) {
      console.log('   ❌ Error de conexión:', testError);
      return;
    }
    
    console.log('   ✅ Conexión exitosa');
    
    // Test 2: Inserción simple
    console.log('\n3. 🔍 Probando inserción simple...');
    
    const simpleData = {
      medio_origen: 'test_alt',
      header: 'Header alternativo',
      title: 'Título alternativo de prueba',
      content: 'Contenido alternativo de prueba que tiene más de 50 caracteres para cumplir con la validación.'
    };
    
    const { data: insertData, error: insertError } = await client
      .from('diarios')
      .insert(simpleData)
      .select()
      .single();
    
    if (insertError) {
      console.log('   ❌ Error en inserción:');
      console.log('      - Mensaje:', insertError.message || 'undefined');
      console.log('      - Código:', insertError.code || 'undefined');
      console.log('      - Detalles:', insertError.details || 'undefined');
      console.log('      - Hint:', insertError.hint || 'undefined');
      console.log('      - Error completo:', JSON.stringify(insertError, null, 2));
      
      // Intentar con upsert
      console.log('\n4. 🔍 Probando con upsert...');
      const { data: upsertData, error: upsertError } = await client
        .from('diarios')
        .upsert(simpleData)
        .select()
        .single();
      
      if (upsertError) {
        console.log('   ❌ Error en upsert:', JSON.stringify(upsertError, null, 2));
      } else {
        console.log('   ✅ Upsert exitoso:', upsertData.id);
      }
      
    } else {
      console.log('   ✅ Inserción exitosa:', insertData.id);
    }
    
    // Test 3: Verificar datos
    console.log('\n5. 🔍 Verificando datos finales...');
    const { data: finalData, error: finalError } = await client
      .from('diarios')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(3);
    
    if (finalError) {
      console.log('   ❌ Error consultando:', finalError.message);
    } else {
      console.log('   ✅ Datos encontrados:', finalData.length, 'registros');
      finalData.forEach((item, index) => {
        console.log(`      ${index + 1}. ${item.medio_origen} - ${item.title}`);
      });
    }
    
  } catch (error) {
    console.error('\n💥 Error general:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Ejecutar test
if (require.main === module) {
  testAlternativeClient();
}

module.exports = { testAlternativeClient };
