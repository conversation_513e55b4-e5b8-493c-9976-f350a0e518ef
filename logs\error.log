2025-07-17 02:02:20 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "TypeError: fetch failed",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:02:27 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "TypeError: fetch failed",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:05:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.information_schema.tables\" does not exist",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:06:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.medios\" does not exist",
  "context": {
    "context": "getMediosActivos",
    "code": "42P01",
    "message": "relation \"public.medios\" does not exist",
    "details": null,
    "hint": null
  }
}
2025-07-17 02:06:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:06:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:06:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:08:16 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createNoticia"
  }
}
2025-07-17 02:08:16 [ERROR]: Error en el sistema
Error: Error guardando noticia en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando noticia en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:25:00 [ERROR]: Error de Supabase al insertar diario
Metadata: {}
2025-07-17 02:25:00 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:25:00 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:25:00 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:30:19 [ERROR]: Error de Supabase al insertar diario
Metadata: {}
2025-07-17 02:30:19 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:30:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:30:19 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:31:22 [ERROR]: Error de Supabase al insertar diario
Metadata: {
  "fullError": "{}"
}
2025-07-17 02:31:22 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:31:22 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:31:22 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:35:31 [ERROR]: Error de Supabase al insertar diario
Metadata: {
  "fullError": "{}"
}
2025-07-17 02:35:31 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:35:31 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:35:31 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:43:36 [ERROR]: Error en SQL directo
Metadata: {
  "error": "Could not find the function public.exec_sql(sql_query) in the schema cache",
  "code": "PGRST202",
  "details": "Searched for the function public.exec_sql with parameter sql_query or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.",
  "hint": null
}
2025-07-17 02:43:36 [ERROR]: Error en createDiario híbrido
Metadata: {
  "error": "Could not find the function public.exec_sql(sql_query) in the schema cache",
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:43:36 [ERROR]: Error en servicio híbrido
Metadata: {
  "error": {
    "code": "HYBRID_SQL_ERROR",
    "message": "Could not find the function public.exec_sql(sql_query) in the schema cache",
    "originalError": {
      "code": "PGRST202",
      "details": "Searched for the function public.exec_sql with parameter sql_query or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.",
      "hint": null,
      "message": "Could not find the function public.exec_sql(sql_query) in the schema cache"
    }
  },
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:43:36 [ERROR]: Error en createDiario
Error: Could not find the function public.exec_sql(sql_query) in the schema cache
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "error": "Could not find the function public.exec_sql(sql_query) in the schema cache",
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:43:36 [ERROR]: Error en el sistema
Error: Could not find the function public.exec_sql(sql_query) in the schema cache
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "type": "system_error",
  "error": "Could not find the function public.exec_sql(sql_query) in the schema cache",
  "context": {
    "context": "createDiario",
    "message": "Could not find the function public.exec_sql(sql_query) in the schema cache"
  }
}
2025-07-17 02:43:36 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "message": "Could not find the function public.exec_sql(sql_query) in the schema cache",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:45:07 [ERROR]: Error en createDiario híbrido
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:45:07 [ERROR]: Error en servicio híbrido
Metadata: {
  "error": {
    "code": "HYBRID_INSERT_ERROR",
    "originalError": {}
  },
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:45:07 [ERROR]: Error en createDiario
Error
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "error": "",
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:45:07 [ERROR]: Error en el sistema
Error
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "type": "system_error",
  "error": "",
  "context": {
    "context": "createDiario",
    "message": ""
  }
}
2025-07-17 02:45:07 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "message": "",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:46:20 [ERROR]: Inserción sin select también falló
Metadata: {
  "fullError": "{}"
}
2025-07-17 02:46:20 [ERROR]: Error en createDiario híbrido
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:46:20 [ERROR]: Error en servicio híbrido
Metadata: {
  "error": {
    "code": "HYBRID_INSERT_ERROR",
    "originalError": {}
  },
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:46:20 [ERROR]: Error en createDiario
Error
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "error": "",
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:46:20 [ERROR]: Error en el sistema
Error
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "type": "system_error",
  "error": "",
  "context": {
    "context": "createDiario",
    "message": ""
  }
}
2025-07-17 02:46:20 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "message": "",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:55:44 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 02:55:45 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "PromptService.savePromptForMedio",
    "medio": "test_medio_custom"
  }
}
2025-07-17 02:55:45 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio_custom"
  }
}
2025-07-17 02:55:46 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getAllActivePrompts"
  }
}
2025-07-17 02:55:46 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 02:58:05 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "\"failed to parse logic tree ((news_processing.status.is.null,news_processing.status.eq.pending))\" (line 1, column 20)",
  "context": {
    "context": "ProcessingService.getPendingNews"
  }
}
2025-07-17 03:01:52 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 03:01:52 [ERROR]: Error en el sistema
Error: OpenAI service no está disponible
    at OpenAIService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\openaiService.js:77:15)
    at ProcessingService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\processingService.js:77:48)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Immediate.<anonymous> (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:83:34)
Metadata: {
  "type": "system_error",
  "error": "OpenAI service no está disponible",
  "context": {
    "context": "OpenAIService.processNews",
    "newsId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
    "medio": "test_medio"
  }
}
2025-07-17 03:01:52 [ERROR]: Error en el sistema
Error: Error en OpenAI: OpenAI service no está disponible
    at ProcessingService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\processingService.js:84:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Immediate.<anonymous> (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:83:34)
Metadata: {
  "type": "system_error",
  "error": "Error en OpenAI: OpenAI service no está disponible",
  "context": {
    "context": "ProcessingService.processNews",
    "processingId": "proc_1752732111989_t16g16n1p",
    "newsId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
    "medio": "test_medio"
  }
}
2025-07-17 03:01:52 [ERROR]: Error en procesamiento automático
Metadata: {
  "diarioId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "error": "Error en OpenAI: OpenAI service no está disponible"
}
