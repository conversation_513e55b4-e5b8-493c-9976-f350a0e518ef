2025-07-17 02:02:20 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "TypeError: fetch failed",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:02:27 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "TypeError: fetch failed",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:05:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.information_schema.tables\" does not exist",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:06:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.medios\" does not exist",
  "context": {
    "context": "getMediosActivos",
    "code": "42P01",
    "message": "relation \"public.medios\" does not exist",
    "details": null,
    "hint": null
  }
}
2025-07-17 02:06:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:06:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:06:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:08:16 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createNoticia"
  }
}
2025-07-17 02:08:16 [ERROR]: Error en el sistema
Error: Error guardando noticia en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando noticia en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:25:00 [ERROR]: Error de Supabase al insertar diario
Metadata: {}
2025-07-17 02:25:00 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:25:00 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:25:00 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:30:19 [ERROR]: Error de Supabase al insertar diario
Metadata: {}
2025-07-17 02:30:19 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:30:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:30:19 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:31:22 [ERROR]: Error de Supabase al insertar diario
Metadata: {
  "fullError": "{}"
}
2025-07-17 02:31:22 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:31:22 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:31:22 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:35:31 [ERROR]: Error de Supabase al insertar diario
Metadata: {
  "fullError": "{}"
}
2025-07-17 02:35:31 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:35:31 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:35:31 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
