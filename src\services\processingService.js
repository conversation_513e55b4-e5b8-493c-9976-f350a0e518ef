// Servicio de procesamiento automático de noticias
const openaiService = require('./openaiService');
const promptService = require('./promptService');
const databaseService = require('./databaseService');
const monitoringService = require('./monitoringService');
const { logger, logError } = require('../utils/logger');

class ProcessingService {
  constructor() {
    this.isProcessing = false;
    this.processingQueue = [];
    this.maxConcurrentProcessing = 3;
    this.currentProcessing = 0;
    this.processingStats = {
      totalProcessed: 0,
      successCount: 0,
      errorCount: 0,
      totalTokensUsed: 0,
      totalProcessingTime: 0
    };
  }

  // Inicializar servicio
  async initialize() {
    try {
      // Inicializar servicios dependientes
      const openaiInit = openaiService.initialize();
      const promptInit = promptService.initialize();
      const dbInit = databaseService.initialize();

      logger.info('Servicios de procesamiento inicializados', {
        openai: openaiInit,
        prompts: promptInit,
        database: dbInit
      });

      return openaiInit && promptInit && dbInit;
    } catch (error) {
      logError(error, { context: 'ProcessingService.initialize' });
      return false;
    }
  }

  // Procesar una noticia específica
  async processNews(newsData, options = {}) {
    const startTime = Date.now();
    const processingId = `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      logger.info('Iniciando procesamiento de noticia', {
        processingId,
        newsId: newsData.id,
        medio: newsData.medio_origen,
        options
      });

      // 1. Crear registro de procesamiento
      const processingRecord = await this.createProcessingRecord(newsData.id, processingId);
      
      if (!processingRecord.success) {
        throw new Error('No se pudo crear registro de procesamiento');
      }

      // 2. Obtener prompt personalizado para el medio
      const promptResult = await promptService.getPromptForMedio(newsData.medio_origen);
      
      if (!promptResult.success) {
        throw new Error('No se pudo obtener prompt para el medio');
      }

      logger.info('Prompt obtenido para procesamiento', {
        processingId,
        isCustom: promptResult.data.isCustom,
        medio: newsData.medio_origen
      });

      // 3. Procesar con OpenAI
      const openaiResult = await openaiService.processNews(
        newsData,
        promptResult.data.template,
        promptResult.data.config
      );

      if (!openaiResult.success) {
        throw new Error(`Error en OpenAI: ${openaiResult.error.message}`);
      }

      const processingTime = Date.now() - startTime;

      // 4. Actualizar registro de procesamiento con resultado exitoso
      const updateResult = await this.updateProcessingRecord(
        processingRecord.data.id,
        'completed',
        {
          tokensUsed: openaiResult.data.processing.tokensUsed,
          processingTime,
          processedContent: openaiResult.data.processedContent,
          model: openaiResult.data.processing.model
        }
      );

      // 5. Actualizar estadísticas
      this.updateStats({
        success: true,
        tokensUsed: openaiResult.data.processing.tokensUsed,
        processingTime
      });

      // 6. Registrar en monitoreo
      monitoringService.recordProcessing(true, {
        tokensUsed: openaiResult.data.processing.tokensUsed,
        processingTime,
        model: openaiResult.data.processing.model
      });

      logger.info('Procesamiento completado exitosamente', {
        processingId,
        newsId: newsData.id,
        tokensUsed: openaiResult.data.processing.tokensUsed,
        processingTime,
        outputLength: openaiResult.data.processedContent.length
      });

      return {
        success: true,
        data: {
          processingId,
          newsId: newsData.id,
          processedContent: openaiResult.data.processedContent,
          processing: openaiResult.data.processing,
          prompt: promptResult.data,
          processingRecord: updateResult.data
        }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;

      logError(error, {
        context: 'ProcessingService.processNews',
        processingId,
        newsId: newsData.id,
        medio: newsData.medio_origen
      });

      // Actualizar registro con error
      try {
        await this.updateProcessingRecord(
          processingId,
          'failed',
          {
            errorMessage: error.message,
            processingTime
          }
        );
      } catch (updateError) {
        logError(updateError, { context: 'Error updating failed processing record' });
      }

      // Actualizar estadísticas
      this.updateStats({
        success: false,
        processingTime
      });

      // Registrar error en monitoreo
      monitoringService.recordProcessing(false, {
        error: error,
        processingTime
      });

      return {
        success: false,
        error: {
          code: 'PROCESSING_ERROR',
          message: error.message,
          processingId,
          originalError: error
        }
      };
    }
  }

  // Procesar noticias pendientes en lote
  async processPendingNews(limit = 10) {
    try {
      if (this.isProcessing) {
        logger.warn('Procesamiento ya en curso, saltando ejecución');
        return { success: false, message: 'Procesamiento ya en curso' };
      }

      this.isProcessing = true;
      logger.info('Iniciando procesamiento de noticias pendientes', { limit });

      // Obtener noticias pendientes
      const pendingNews = await this.getPendingNews(limit);
      
      if (!pendingNews.success || pendingNews.data.length === 0) {
        logger.info('No hay noticias pendientes para procesar');
        this.isProcessing = false;
        return { success: true, message: 'No hay noticias pendientes', processed: 0 };
      }

      logger.info('Noticias pendientes encontradas', { count: pendingNews.data.length });

      const results = [];
      
      // Procesar cada noticia
      for (const news of pendingNews.data) {
        if (this.currentProcessing >= this.maxConcurrentProcessing) {
          // Esperar si hay demasiados procesamientos concurrentes
          await this.waitForProcessingSlot();
        }

        this.currentProcessing++;
        
        try {
          const result = await this.processNews(news);
          results.push(result);
        } catch (error) {
          results.push({
            success: false,
            error: error.message,
            newsId: news.id
          });
        } finally {
          this.currentProcessing--;
        }
      }

      this.isProcessing = false;

      const successCount = results.filter(r => r.success).length;
      const errorCount = results.filter(r => !r.success).length;

      logger.info('Procesamiento en lote completado', {
        total: results.length,
        success: successCount,
        errors: errorCount
      });

      return {
        success: true,
        data: {
          total: results.length,
          success: successCount,
          errors: errorCount,
          results
        }
      };

    } catch (error) {
      this.isProcessing = false;
      logError(error, { context: 'ProcessingService.processPendingNews' });
      
      return {
        success: false,
        error: {
          code: 'BATCH_PROCESSING_ERROR',
          message: error.message
        }
      };
    }
  }

  // Obtener noticias pendientes de procesamiento
  async getPendingNews(limit = 10) {
    try {
      // Primero obtener todas las noticias
      const { data: allNews, error: newsError } = await databaseService.client
        .from('diarios')
        .select('*')
        .order('created_at', { ascending: true })
        .limit(limit * 2); // Obtener más para filtrar después

      if (newsError) {
        throw newsError;
      }

      // Luego obtener registros de procesamiento
      const { data: processings, error: procError } = await databaseService.client
        .from('news_processing')
        .select('diario_id, status')
        .in('status', ['completed', 'processing']);

      if (procError && procError.code !== 'PGRST116') {
        throw procError;
      }

      // Filtrar noticias que no han sido procesadas o están pendientes
      const processedIds = new Set(
        (processings || [])
          .filter(p => p.status === 'completed')
          .map(p => p.diario_id)
      );

      const pendingNews = allNews
        .filter(news => !processedIds.has(news.id))
        .slice(0, limit);

      logger.info('Noticias pendientes filtradas', {
        total: allNews.length,
        processed: processedIds.size,
        pending: pendingNews.length
      });

      return { success: true, data: pendingNews };

    } catch (error) {
      logError(error, { context: 'ProcessingService.getPendingNews' });
      return {
        success: false,
        error: {
          code: 'PENDING_NEWS_FETCH_ERROR',
          message: error.message
        }
      };
    }
  }

  // Crear registro de procesamiento
  async createProcessingRecord(newsId, processingId) {
    try {
      const result = await databaseService.createNewsProcessing(newsId, 'processing');
      
      if (!result.success) {
        throw new Error(result.error.message);
      }

      return result;

    } catch (error) {
      logError(error, { context: 'ProcessingService.createProcessingRecord' });
      return {
        success: false,
        error: {
          code: 'PROCESSING_RECORD_CREATE_ERROR',
          message: error.message
        }
      };
    }
  }

  // Actualizar registro de procesamiento
  async updateProcessingRecord(recordId, status, data = {}) {
    try {
      const updateData = {
        status,
        updated_at: new Date().toISOString()
      };

      if (status === 'completed') {
        updateData.completed_at = new Date().toISOString();
        updateData.openai_tokens_used = data.tokensUsed;
        updateData.processing_time_ms = data.processingTime;
      } else if (status === 'failed') {
        updateData.error_message = data.errorMessage;
        updateData.processing_time_ms = data.processingTime;
      }

      // Aquí usaríamos el método de actualización del databaseService
      // Por ahora simulamos el éxito
      return {
        success: true,
        data: { id: recordId, ...updateData }
      };

    } catch (error) {
      logError(error, { context: 'ProcessingService.updateProcessingRecord' });
      return {
        success: false,
        error: {
          code: 'PROCESSING_RECORD_UPDATE_ERROR',
          message: error.message
        }
      };
    }
  }

  // Actualizar estadísticas de procesamiento
  updateStats(data) {
    this.processingStats.totalProcessed++;
    
    if (data.success) {
      this.processingStats.successCount++;
      this.processingStats.totalTokensUsed += data.tokensUsed || 0;
    } else {
      this.processingStats.errorCount++;
    }
    
    this.processingStats.totalProcessingTime += data.processingTime || 0;
  }

  // Esperar por un slot de procesamiento disponible
  async waitForProcessingSlot() {
    while (this.currentProcessing >= this.maxConcurrentProcessing) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // Obtener estadísticas de procesamiento
  getStats() {
    return {
      ...this.processingStats,
      isProcessing: this.isProcessing,
      currentProcessing: this.currentProcessing,
      maxConcurrentProcessing: this.maxConcurrentProcessing,
      averageProcessingTime: this.processingStats.totalProcessed > 0 
        ? this.processingStats.totalProcessingTime / this.processingStats.totalProcessed 
        : 0,
      successRate: this.processingStats.totalProcessed > 0 
        ? (this.processingStats.successCount / this.processingStats.totalProcessed) * 100 
        : 0
    };
  }

  // Resetear estadísticas
  resetStats() {
    this.processingStats = {
      totalProcessed: 0,
      successCount: 0,
      errorCount: 0,
      totalTokensUsed: 0,
      totalProcessingTime: 0
    };
    
    logger.info('Estadísticas de procesamiento reseteadas');
  }
}

// Exportar instancia singleton
const processingService = new ProcessingService();

module.exports = processingService;
