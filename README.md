# Demo IA Server - Sistema de Procesamiento Automatizado de Noticias

Sistema backend robusto para el procesamiento automatizado de noticias utilizando OpenAI API y Supabase.

## 🚀 Características

- **Webhook Seguro**: Endpoint POST `/webhook/news` con validación de firma HMAC
- **Validación Robusta**: Esquemas Joi para validación de datos entrantes
- **Rate Limiting**: Protección contra abuso con límites configurables
- **Logging Estructurado**: Sistema de logs con Winston para monitoreo
- **Autenticación**: Validación de origen y firma de webhooks
- **Health Checks**: Endpoints para monitoreo de salud del sistema

## 📋 Requisitos

- Node.js >= 18.0.0
- npm o yarn
- Cuenta de Supabase
- API Key de OpenAI

## 🛠️ Instalación

1. **Clonar el repositorio**
```bash
git clone <repository-url>
cd demo-ia-server
```

2. **Instalar dependencias**
```bash
npm install
```

3. **Configurar variables de entorno**
```bash
cp .env.example .env
```

Editar `.env` con tus credenciales:
```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Supabase Configuration
SUPABASE_URL=tu_supabase_url
SUPABASE_ANON_KEY=tu_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=tu_supabase_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=tu_openai_api_key

# Webhook Security
WEBHOOK_SECRET=tu_webhook_secret_seguro
```

4. **Crear directorio de logs**
```bash
mkdir logs
```

## 🚀 Uso

### Desarrollo
```bash
npm run dev
```

### Producción
```bash
npm start
```

## 📡 Endpoints

### Webhook Principal
```
POST /webhook/news
```

**Headers requeridos:**
- `Content-Type: application/json`
- `x-webhook-signature: sha256=<firma_hmac>`

**Payload ejemplo:**
```json
{
  "medio_origen": "ejemplo_medio",
  "header": "Titular de la noticia",
  "title": "Título completo de la noticia",
  "summary": "Resumen de la noticia",
  "content": "Contenido completo de la noticia...",
  "images_url": ["https://ejemplo.com/imagen1.jpg"],
  "categories": ["tecnología", "innovación"]
}
```

**Nota:** Los datos se almacenan en la tabla `diarios` en Supabase.

### Health Checks
```
GET /health          # Estado general del sistema
GET /ping            # Verificación simple de conectividad
GET /ready           # Readiness check para deployment
```

### Consulta de Estado
```
GET /webhook/news/:id/status    # Estado de procesamiento de noticia
```

## 🔒 Seguridad

### Validación de Firma HMAC
Todas las requests al webhook deben incluir una firma HMAC SHA-256:

```javascript
const crypto = require('crypto');
const signature = crypto
  .createHmac('sha256', WEBHOOK_SECRET)
  .update(JSON.stringify(payload))
  .digest('hex');

// Header: x-webhook-signature: sha256=<signature>
```

### Rate Limiting
- **Webhook**: 100 requests por 15 minutos
- **Consultas**: 10 requests por minuto
- **Por IP**: Límites configurables

## 📊 Logging

Los logs se almacenan en:
- `logs/app.log` - Log general
- `logs/error.log` - Solo errores

Formato estructurado con timestamp, nivel y metadata.

## 🧪 Testing

```bash
# Ejecutar tests
npm test

# Tests en modo watch
npm run test:watch

# Coverage
npm run test:coverage
```

## 📁 Estructura del Proyecto

```
demo-ia-server/
├── src/
│   ├── config/          # Configuración
│   ├── controllers/     # Controladores
│   ├── middleware/      # Middleware personalizado
│   ├── models/          # Esquemas y validaciones
│   ├── routes/          # Definición de rutas
│   └── utils/           # Utilidades
├── logs/                # Archivos de log
├── tests/               # Tests
├── .env.example         # Ejemplo de variables de entorno
├── server.js            # Punto de entrada
└── package.json
```

## 🔄 Fases de Implementación

- [x] **Fase 1**: Setup básico y endpoint webhook
- [x] **Fase 2**: Integración con Supabase
- [ ] **Fase 3**: Integración con OpenAI API
- [ ] **Fase 4**: Sistema de prompts por medio
- [ ] **Fase 5**: Testing y validación completa

## 📝 Configuración de Supabase

### Configuración Inicial
1. Crear proyecto en [Supabase](https://supabase.com)
2. Obtener credenciales (URL, anon key, service role key)
3. Actualizar archivo `.env` con las credenciales
4. Ejecutar script de configuración:

```bash
# Crear esquema de base de datos
node scripts/setup-database.js
```

Ver guía completa en: `docs/SUPABASE_SETUP.md`

### Verificar Configuración
```bash
# Health check con estado de BD
curl http://localhost:3000/health

# Readiness check detallado
curl http://localhost:3000/ready
```

## 📝 Próximos Pasos

1. ✅ Configurar base de datos Supabase
2. Implementar integración con OpenAI
3. Crear sistema de prompts dinámicos
4. Agregar tests completos
5. Configurar deployment

## 🤝 Contribución

1. Fork el proyecto
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📄 Licencia

MIT License - ver archivo LICENSE para detalles.
