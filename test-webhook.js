const crypto = require('crypto');
const https = require('http');

// Configuración
const WEBHOOK_SECRET = 'mi_webhook_secret_super_seguro_para_testing_123';
const WEBHOOK_URL = 'http://localhost:3000/webhook/news';

// Datos de prueba
const testNewsData = {
  medio_origen: "test_medio",
  header: "Titular de prueba para testing del webhook",
  title: "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
  summary: "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
  content: "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de procesamiento automatizado que utiliza inteligencia artificial para reescribir noticias según diferentes formatos de medios de comunicación. Este sistema integra OpenAI API con Supabase para almacenar tanto las versiones originales como las reescritas de las noticias.",
  images_url: ["https://ejemplo.com/imagen1.jpg", "https://ejemplo.com/imagen2.jpg"],
  categories: ["tecnología", "inteligencia artificial", "automatización"]
};

// Función para generar firma HMAC
function generateSignature(payload, secret) {
  const signature = crypto
    .createHmac('sha256', secret)
    .update(payload, 'utf8')
    .digest('hex');
  return `sha256=${signature}`;
}

// Función para enviar webhook
function sendWebhook(data) {
  return new Promise((resolve, reject) => {
    const payload = JSON.stringify(data);
    const signature = generateSignature(payload, WEBHOOK_SECRET);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/webhook/news',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(payload),
        'x-webhook-signature': signature,
        'User-Agent': 'Test-Webhook-Client/1.0'
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedResponse = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedResponse
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(payload);
    req.end();
  });
}

// Función principal de testing
async function testWebhook() {
  console.log('🧪 Iniciando test del webhook...\n');
  
  try {
    console.log('📤 Enviando noticia de prueba...');
    console.log('Datos:', JSON.stringify(testNewsData, null, 2));
    console.log('\n⏳ Procesando...\n');
    
    const response = await sendWebhook(testNewsData);
    
    console.log('📥 Respuesta recibida:');
    console.log('Status Code:', response.statusCode);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    if (response.statusCode === 200 && response.data.success) {
      console.log('\n✅ Test exitoso! El webhook está funcionando correctamente.');
    } else {
      console.log('\n❌ Test fallido. Revisar la respuesta del servidor.');
    }
    
  } catch (error) {
    console.error('\n💥 Error durante el test:', error.message);
  }
}

// Función para test con datos inválidos
async function testInvalidData() {
  console.log('\n🧪 Probando con datos inválidos...\n');
  
  const invalidData = {
    medio_origen: "test",
    // Falta header (requerido)
    title: "Título muy corto", // Muy corto
    content: "Contenido muy corto" // Muy corto
  };
  
  try {
    const response = await sendWebhook(invalidData);
    console.log('📥 Respuesta para datos inválidos:');
    console.log('Status Code:', response.statusCode);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    if (response.statusCode === 400) {
      console.log('\n✅ Validación funcionando correctamente - datos inválidos rechazados.');
    } else {
      console.log('\n❌ La validación no está funcionando como se esperaba.');
    }
    
  } catch (error) {
    console.error('\n💥 Error durante test de validación:', error.message);
  }
}

// Función para test sin firma
async function testWithoutSignature() {
  console.log('\n🧪 Probando sin firma de autenticación...\n');
  
  return new Promise((resolve, reject) => {
    const payload = JSON.stringify(testNewsData);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/webhook/news',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(payload),
        // Sin x-webhook-signature
        'User-Agent': 'Test-Webhook-Client/1.0'
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedResponse = JSON.parse(responseData);
          console.log('📥 Respuesta sin firma:');
          console.log('Status Code:', res.statusCode);
          console.log('Response:', JSON.stringify(parsedResponse, null, 2));
          
          if (res.statusCode === 401) {
            console.log('\n✅ Autenticación funcionando correctamente - request sin firma rechazada.');
          } else {
            console.log('\n❌ La autenticación no está funcionando como se esperaba.');
          }
          
          resolve();
        } catch (error) {
          console.error('Error parsing response:', error);
          resolve();
        }
      });
    });

    req.on('error', (error) => {
      console.error('Error en request:', error);
      resolve();
    });

    req.write(payload);
    req.end();
  });
}

// Ejecutar todos los tests
async function runAllTests() {
  console.log('🚀 Demo IA Server - Test Suite del Webhook\n');
  console.log('=' * 50);
  
  await testWebhook();
  await testInvalidData();
  await testWithoutSignature();
  
  console.log('\n' + '=' * 50);
  console.log('🏁 Tests completados!');
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  runAllTests();
}

module.exports = {
  sendWebhook,
  generateSignature,
  testNewsData
};
