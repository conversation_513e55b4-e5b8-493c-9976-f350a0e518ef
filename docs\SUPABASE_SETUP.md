# Configuración de Supabase - Demo IA Server

## 🚀 Pasos para Configurar Supabase

### 1. Crear Proyecto en Supabase

1. Ve a [https://supabase.com](https://supabase.com)
2. Crea una cuenta o inicia sesión
3. Haz clic en "New Project"
4. Completa los datos:
   - **Name**: `demo-ia-server`
   - **Database Password**: Genera una contraseña segura
   - **Region**: Selecciona la región más cercana
5. Haz clic en "Create new project"
6. Espera a que el proyecto se inicialice (2-3 minutos)

### 2. Obtener Credenciales

Una vez creado el proyecto:

1. Ve a **Settings** → **API**
2. Copia las siguientes credenciales:
   - **Project URL**: `https://tu-proyecto.supabase.co`
   - **anon public key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
   - **service_role secret key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### 3. Configurar Variables de Entorno

Actualiza tu archivo `.env`:

```env
# Supabase Configuration
SUPABASE_URL=https://tu-proyecto.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 4. Crear Esquema de Base de Datos

1. Ve a **SQL Editor** en tu dashboard de Supabase
2. Copia y pega el contenido completo de `database/schema.sql`
3. Haz clic en "Run" para ejecutar el script
4. Verifica que no haya errores

### 5. Verificar Configuración

Ejecuta el script de verificación:

```bash
node scripts/setup-database.js
```

Deberías ver:
```
✅ Conexión con Supabase establecida correctamente
✅ Todas las tablas ya existen
✅ Datos de prueba creados
🎉 Configuración de base de datos completada!
```

## 📊 Estructura de Tablas Creadas

### `noticias`
- Almacena las noticias recibidas via webhook
- Campos: id, medio_origen, header, title, summary, content, images_url, categories

### `medios`
- Configuración de medios de comunicación
- Campos: id, codigo, nombre, descripcion, activo

### `prompts_medios`
- Prompts personalizados por medio
- Campos: id, medio_id, tipo_prompt, prompt_template, activo

### `system_logs`
- Logs del sistema en base de datos
- Campos: id, level, message, metadata, created_at

### `news_processing`
- Seguimiento del procesamiento de noticias
- Campos: id, noticia_id, status, openai_tokens_used, processing_time_ms

## 🔒 Configuración de Seguridad

### Row Level Security (RLS)
- Todas las tablas tienen RLS habilitado
- Solo el service role puede realizar operaciones
- Configuración segura por defecto

### Políticas de Acceso
- Service role: Acceso completo a todas las tablas
- Anon role: Sin acceso directo (para futuras funcionalidades)

## 🧪 Testing de la Configuración

### Test de Conexión
```bash
# Verificar conexión
node -e "
const { testSupabaseConnection } = require('./src/config/database');
testSupabaseConnection().then(result => 
  console.log('Conexión:', result ? '✅ OK' : '❌ Error')
);
"
```

### Test de Tablas
```bash
# Verificar tablas
node -e "
const { checkTablesExist } = require('./src/config/database');
checkTablesExist().then(result => 
  console.log('Tablas:', result.allTablesExist ? '✅ Todas' : '❌ Faltan:', result.missingTables)
);
"
```

### Test Completo
```bash
# Ejecutar tests de integración
npm test tests/integration/database.test.js
```

## 🚨 Troubleshooting

### Error: "fetch failed"
- Verifica que SUPABASE_URL sea correcto
- Asegúrate de que el proyecto esté activo
- Revisa tu conexión a internet

### Error: "Invalid API key"
- Verifica que SUPABASE_SERVICE_ROLE_KEY sea correcto
- Asegúrate de usar el service_role key, no el anon key

### Error: "relation does not exist"
- Las tablas no están creadas
- Ejecuta el script SQL en Supabase Dashboard
- Verifica que no haya errores en la ejecución

### Error: "permission denied"
- Problema con RLS o políticas
- Verifica que uses el service_role key
- Revisa las políticas en Supabase Dashboard

## 📝 Próximos Pasos

Una vez configurado Supabase:

1. ✅ Ejecutar tests de integración
2. ✅ Probar webhook con base de datos real
3. ✅ Verificar health checks
4. 🔄 Continuar con Fase 3 (OpenAI Integration)

## 🔗 Enlaces Útiles

- [Supabase Dashboard](https://app.supabase.com)
- [Documentación Supabase](https://supabase.com/docs)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript)
- [SQL Editor Guide](https://supabase.com/docs/guides/database/sql-editor)
