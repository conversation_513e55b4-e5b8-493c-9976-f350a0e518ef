// PM2 Ecosystem Configuration for Demo IA Server
module.exports = {
  apps: [
    {
      // Aplicación principal
      name: 'demo-ia-server',
      script: 'server.js',
      instances: 'max', // Usar todos los cores disponibles
      exec_mode: 'cluster',
      
      // Variables de entorno
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 3000
      },
      
      // Configuración de logs
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Configuración de memoria y restart
      max_memory_restart: '500M',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Configuración de monitoreo
      monitoring: true,
      pmx: true,
      
      // Configuración de autorestart
      autorestart: true,
      watch: false, // Deshabilitado en producción
      ignore_watch: [
        'node_modules',
        'logs',
        '.git',
        '*.log'
      ],
      
      // Variables adicionales
      node_args: '--max-old-space-size=512',
      
      // Health check
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      
      // Configuración de cluster
      listen_timeout: 8000,
      kill_timeout: 5000,
      
      // Source map support
      source_map_support: true,
      
      // Configuración de cron para tareas programadas
      cron_restart: '0 2 * * *', // Restart diario a las 2 AM
      
      // Configuración de deployment
      post_update: ['npm install', 'echo "Deployment completed"'],
      
      // Configuración de logs avanzada
      log_type: 'json',
      
      // Configuración de errores
      error_file: './logs/error.log',
      combine_logs: true,
      
      // Configuración de tiempo
      time: true
    },
    
    // Worker para procesamiento en background (opcional)
    {
      name: 'demo-ia-worker',
      script: 'src/workers/backgroundProcessor.js',
      instances: 1,
      exec_mode: 'fork',
      
      env: {
        NODE_ENV: 'development',
        WORKER_TYPE: 'background_processor'
      },
      env_production: {
        NODE_ENV: 'production',
        WORKER_TYPE: 'background_processor'
      },
      
      // Configuración específica del worker
      max_memory_restart: '256M',
      restart_delay: 5000,
      max_restarts: 5,
      min_uptime: '30s',
      
      // Logs separados para el worker
      log_file: './logs/worker-combined.log',
      out_file: './logs/worker-out.log',
      error_file: './logs/worker-error.log',
      
      // Configuración de cron para el worker
      cron_restart: '0 3 * * *', // Restart diario a las 3 AM
      
      // Deshabilitar por defecto (habilitar solo si es necesario)
      autorestart: false,
      
      // Variables específicas del worker
      env_vars: {
        WORKER_CONCURRENCY: 3,
        WORKER_TIMEOUT: 30000
      }
    }
  ],

  // Configuración de deployment
  deploy: {
    // Producción
    production: {
      user: 'demoai',
      host: ['your-production-server.com'],
      ref: 'origin/main',
      repo: '**************:your-username/demo-ia-server.git',
      path: '/var/www/demo-ia-server',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },
    
    // Staging
    staging: {
      user: 'demoai',
      host: ['your-staging-server.com'],
      ref: 'origin/develop',
      repo: '**************:your-username/demo-ia-server.git',
      path: '/var/www/demo-ia-server-staging',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env staging',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};
