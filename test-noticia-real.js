// Test con noticia real de Telesol Diario
const crypto = require('crypto');
const config = require('./src/config/environment');

async function testNoticiaReal() {
  console.log('📰 Test con Noticia Real - Telesol Diario\n');
  
  try {
    // Datos de la noticia real extraída
    const noticiaReal = {
      medio_origen: "telesol_diario",
      header: "Evento suspendido",
      title: "Convocatoria en la Plaza 25: La Municipalidad de la Ciudad de San Juan emitió un comunicado",
      summary: "El evento de '<PERSON>' fue suspendido por la Municipalidad tras una convocatoria inesperadamente grande. Se recordó que no se permiten actos masivos en la plaza.",
      content: `El influencer <PERSON>, conocido como "Peter <PERSON>", reconoció que esperaba solo 50 personas, pero la masiva convocatoria superó todos los cálculos y causó momentos de tensión en el microcentro. No hubo heridos de gravedad.

La Municipalidad de la Ciudad de San Juan, recordó que la Plaza 25 de Mayo es Monumento Histórico y está prohibido hacer actos masivos, salvo que sean institucionales. El evento fue interrumpido por la falta de medidas de seguridad y el caos generado.

Comunicado:

La Municipalidad de la Ciudad de San Juan recuerda que la Plaza 25 de Mayo no puede ser utilizada para convocatorias de tipo masivas por estar declarada Monumento Histórico.

Esa preservación por ley impide que sea utilizada para actos que no sean estrictamente institucionales.

El influencer había anunciado el evento a través de sus redes sociales, esperando una convocatoria moderada, pero la respuesta del público superó ampliamente las expectativas. La situación generó aglomeraciones que pusieron en riesgo la seguridad de los asistentes y la preservación del espacio histórico.

Las autoridades municipales actuaron rápidamente para dispersar a la multitud y restablecer el orden en la zona céntrica de la ciudad. Se reforzó la presencia policial para evitar incidentes mayores.

Este tipo de eventos requiere autorización previa y medidas de seguridad adecuadas, especialmente cuando se realizan en espacios públicos de valor patrimonial como la Plaza 25 de Mayo.`,
      images_url: [
        "https://www.telesoldiario.com/content/bucket/1/463431w850h479c.jpg.webp"
      ],
      categories: [
        "política",
        "san juan",
        "municipalidad",
        "eventos públicos",
        "patrimonio histórico"
      ],
      source_url: "https://www.telesoldiario.com/455316-convocatoria-en-la-plaza-25-la-municipalidad-de-la-ciudad-de-san-juan-emitio-un-comunicado",
      published_date: "2025-07-16",
      author: "Telesol Diario"
    };

    console.log('1. 📊 Información de la noticia real:');
    console.log('   🏢 Medio:', noticiaReal.medio_origen);
    console.log('   📰 Título:', noticiaReal.title);
    console.log('   📝 Resumen length:', noticiaReal.summary.length);
    console.log('   📄 Contenido length:', noticiaReal.content.length);
    console.log('   🖼️  Imágenes:', noticiaReal.images_url.length);
    console.log('   🏷️  Categorías:', noticiaReal.categories.join(', '));
    console.log('   🔗 URL original:', noticiaReal.source_url);

    console.log('\n2. 🚀 Enviando noticia real al webhook...');

    // Crear firma HMAC para autenticación (igual que en test-webhook.js)
    const payload = JSON.stringify(noticiaReal);
    const signature = crypto
      .createHmac('sha256', config.webhook.secret)
      .update(payload, 'utf8')
      .digest('hex');

    console.log('   🔐 Firma HMAC creada');
    console.log('   📦 Payload size:', payload.length, 'bytes');

    // Enviar al webhook
    const startTime = Date.now();
    const response = await fetch('http://localhost:3000/webhook/news', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Test-Noticia-Real/1.0',
        'x-webhook-signature': `sha256=${signature}`
      },
      body: payload
    });

    const responseTime = Date.now() - startTime;
    const result = await response.json();

    console.log('\n3. 📥 Respuesta del webhook:');
    console.log('   Status:', response.status);
    console.log('   Success:', result.success);
    console.log('   Message:', result.message);
    console.log('   Response time:', responseTime, 'ms');

    if (result.success) {
      console.log('\n   ✅ Noticia real guardada exitosamente!');
      console.log('   🆔 ID:', result.data.id);
      console.log('   🏢 Medio:', result.data.medio_origen);
      console.log('   📰 Título:', result.data.title);
      console.log('   📅 Creado:', result.data.created_at);
      console.log('   🔄 Status:', result.data.status);
      console.log('   ⚙️  Processing status:', result.data.processing_status);
      console.log('   🤖 Auto processing:', result.data.auto_processing);

      const newsId = result.data.id;

      console.log('\n4. ⏳ Esperando procesamiento automático...');
      console.log('   (El sistema está procesando la noticia con OpenAI en background)');
      
      // Esperar un poco para que el procesamiento se complete
      await new Promise(resolve => setTimeout(resolve, 15000)); // 15 segundos

      console.log('\n5. 🔍 Verificando estado del procesamiento...');

      const statusResponse = await fetch(`http://localhost:3000/webhook/news/${newsId}/status`, {
        method: 'GET',
        headers: {
          'User-Agent': 'Test-Noticia-Real/1.0'
        }
      });

      const statusResult = await statusResponse.json();

      console.log('   Status code:', statusResponse.status);
      console.log('   Success:', statusResult.success);

      if (statusResult.success) {
        const processing = statusResult.data.processing;
        console.log('   ✅ Estado obtenido:');
        console.log('   📊 Status:', processing.status);
        console.log('   🔢 Tokens usados:', processing.openai_tokens_used || 'N/A');
        console.log('   ⏱️  Tiempo procesamiento:', processing.processing_time_ms || 'N/A', 'ms');
        console.log('   📅 Completado:', processing.completed_at || 'En proceso');

        if (processing.status === 'completed') {
          console.log('\n6. 📄 Obteniendo contenido procesado...');

          const contentResponse = await fetch(`http://localhost:3000/webhook/news/${newsId}/content`, {
            method: 'GET',
            headers: {
              'User-Agent': 'Test-Noticia-Real/1.0'
            }
          });

          const contentResult = await contentResponse.json();

          if (contentResult.success) {
            console.log('   ✅ Contenido procesado obtenido!');
            console.log('   📊 Original length:', contentResult.data.comparison.original_length);
            console.log('   📊 Processed length:', contentResult.data.comparison.processed_length);
            console.log('   🔢 Tokens:', contentResult.data.comparison.tokens_used);
            console.log('   ⏱️  Processing time:', contentResult.data.comparison.processing_time, 'ms');

            console.log('\n   📄 CONTENIDO ORIGINAL vs PROCESADO:');
            console.log('   ' + '='.repeat(80));
            console.log('   ORIGINAL (primeros 200 chars):');
            console.log('   ' + noticiaReal.content.substring(0, 200) + '...');
            console.log('   ' + '-'.repeat(80));
            console.log('   PROCESADO (primeros 200 chars):');
            console.log('   ' + (contentResult.data.processed.content || 'No disponible').substring(0, 200) + '...');
            console.log('   ' + '='.repeat(80));
          } else {
            console.log('   ⚠️  Contenido procesado no disponible:', contentResult.message);
          }
        } else {
          console.log('   ⏳ Procesamiento aún en curso o falló');
          console.log('   💡 Puedes verificar el estado más tarde con:');
          console.log(`   GET /webhook/news/${newsId}/status`);
        }
      } else {
        console.log('   ❌ Error obteniendo estado:', statusResult.error?.message);
      }

      console.log('\n7. 📊 Verificando métricas del sistema...');

      const metricsResponse = await fetch('http://localhost:3000/webhook/metrics', {
        method: 'GET',
        headers: {
          'User-Agent': 'Test-Noticia-Real/1.0'
        }
      });

      const metricsResult = await metricsResponse.json();

      if (metricsResult.success) {
        const metrics = metricsResult.data.metrics;
        console.log('   ✅ Métricas actualizadas:');
        console.log('   📈 Webhooks totales:', metrics.webhooks.total);
        console.log('   📈 Procesamientos totales:', metrics.processing.total);
        console.log('   🔢 Total tokens usados:', metrics.processing.totalTokens);
        console.log('   💰 Costo total:', '$' + metrics.processing.totalCost);
        console.log('   📊 Success rate:', metrics.processing.successRate + '%');
        console.log('   ⏱️  Tiempo promedio:', metrics.processing.averageTime, 'ms');
      }

    } else {
      console.log('\n   ❌ Error enviando noticia:');
      console.log('   Code:', result.error?.code);
      console.log('   Details:', result.error?.details);
    }

    console.log('\n✅ Test con noticia real completado!');
    console.log('\n🎯 Resumen:');
    console.log('   📰 Noticia real de Telesol Diario procesada');
    console.log('   🤖 Sistema OpenAI funcionando con contenido real');
    console.log('   📊 Métricas y monitoreo actualizados');
    console.log('   🔄 Flujo completo webhook → BD → OpenAI → resultado');

  } catch (error) {
    console.error('\n💥 Error en test:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Función para mostrar información de la noticia
function mostrarInfoNoticia() {
  console.log('📰 Información de la Noticia Real\n');
  
  console.log('🏢 Medio: Telesol Diario');
  console.log('📅 Fecha: 16 de Julio, 2025');
  console.log('🏷️  Categoría: Política / San Juan');
  console.log('📰 Título: Convocatoria en la Plaza 25: La Municipalidad de la Ciudad de San Juan emitió un comunicado');
  console.log('🔗 URL: https://www.telesoldiario.com/455316-convocatoria-en-la-plaza-25-la-municipalidad-de-la-ciudad-de-san-juan-emitio-un-comunicado');
  
  console.log('\n📝 Resumen:');
  console.log('El evento de "Peter Regalos" fue suspendido por la Municipalidad tras una convocatoria inesperadamente grande. Se recordó que no se permiten actos masivos en la plaza.');
  
  console.log('\n🎯 Características de la noticia:');
  console.log('   ✅ Noticia local de San Juan');
  console.log('   ✅ Contenido político/administrativo');
  console.log('   ✅ Evento actual y relevante');
  console.log('   ✅ Incluye declaraciones oficiales');
  console.log('   ✅ Contexto histórico (Plaza 25 de Mayo)');
  console.log('   ✅ Personaje público (Peter Regalos)');
  
  console.log('\n💡 Esta noticia es perfecta para probar:');
  console.log('   🔄 Procesamiento de contenido real');
  console.log('   🎯 Adaptación de estilo por medio');
  console.log('   📊 Métricas con contenido auténtico');
  console.log('   🤖 Capacidad de OpenAI con noticias locales');
}

// Ejecutar test
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--info')) {
    mostrarInfoNoticia();
  } else {
    testNoticiaReal();
  }
}

module.exports = { testNoticiaReal, mostrarInfoNoticia };
