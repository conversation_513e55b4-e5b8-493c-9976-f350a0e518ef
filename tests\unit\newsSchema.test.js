const { validateWebhookNews, createApiResponse } = require('../../src/models/newsSchema');

describe('News Schema Validation', () => {
  describe('validateWebhookNews', () => {
    const validNewsData = {
      medio_origen: "test_medio",
      header: "Titular de prueba para testing",
      title: "Noticia de prueba completa",
      summary: "Resumen de la noticia de prueba",
      content: "Contenido completo de la noticia que debe tener al menos 50 caracteres para pasar la validación",
      images_url: ["https://ejemplo.com/imagen1.jpg"],
      categories: ["tecnología", "pruebas"]
    };

    test('should validate correct news data', () => {
      const result = validateWebhookNews(validNewsData);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toBeNull();
      expect(result.data).toEqual(validNewsData);
    });

    test('should reject data without required fields', () => {
      const invalidData = {
        medio_origen: "test_medio"
        // Faltan campos requeridos
      };
      
      const result = validateWebhookNews(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(3); // header, title, content
      expect(result.data).toBeNull();
    });

    test('should reject medio_origen too short', () => {
      const invalidData = {
        ...validNewsData,
        medio_origen: "a" // Muy corto
      };
      
      const result = validateWebhookNews(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors[0].field).toBe('medio_origen');
    });

    test('should reject content too short', () => {
      const invalidData = {
        ...validNewsData,
        content: "Muy corto" // Menos de 50 caracteres
      };
      
      const result = validateWebhookNews(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors[0].field).toBe('content');
    });

    test('should accept empty summary', () => {
      const dataWithEmptySummary = {
        ...validNewsData,
        summary: ""
      };
      
      const result = validateWebhookNews(dataWithEmptySummary);
      
      expect(result.isValid).toBe(true);
    });

    test('should accept string images_url', () => {
      const dataWithStringImage = {
        ...validNewsData,
        images_url: "https://ejemplo.com/imagen.jpg"
      };
      
      const result = validateWebhookNews(dataWithStringImage);
      
      expect(result.isValid).toBe(true);
    });

    test('should accept array categories', () => {
      const dataWithArrayCategories = {
        ...validNewsData,
        categories: ["tech", "news", "ai"]
      };
      
      const result = validateWebhookNews(dataWithArrayCategories);
      
      expect(result.isValid).toBe(true);
    });

    test('should strip unknown fields', () => {
      const dataWithExtraFields = {
        ...validNewsData,
        unknownField: "should be removed",
        anotherField: 123
      };
      
      const result = validateWebhookNews(dataWithExtraFields);
      
      expect(result.isValid).toBe(true);
      expect(result.data.unknownField).toBeUndefined();
      expect(result.data.anotherField).toBeUndefined();
    });
  });

  describe('createApiResponse', () => {
    test('should create success response', () => {
      const response = createApiResponse(true, "Success message", { id: 1 });
      
      expect(response.success).toBe(true);
      expect(response.message).toBe("Success message");
      expect(response.data).toEqual({ id: 1 });
      expect(response.timestamp).toBeDefined();
      expect(response.error).toBeUndefined();
    });

    test('should create error response', () => {
      const error = { code: "TEST_ERROR", details: "Test error details" };
      const response = createApiResponse(false, "Error message", null, error);
      
      expect(response.success).toBe(false);
      expect(response.message).toBe("Error message");
      expect(response.data).toBeUndefined();
      expect(response.error).toBeDefined();
      expect(response.error.code).toBe("TEST_ERROR");
      expect(response.error.details).toBe("Test error details");
    });

    test('should include timestamp', () => {
      const response = createApiResponse(true, "Test");
      
      expect(response.timestamp).toBeDefined();
      expect(new Date(response.timestamp)).toBeInstanceOf(Date);
    });
  });
});
