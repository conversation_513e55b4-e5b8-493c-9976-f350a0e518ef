-- =====================================================
-- DEMO IA SERVER - ESQUEMA DE BASE DE DATOS SUPABASE
-- =====================================================

-- Habilitar extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- TABLA: noticias
-- Almacena las noticias recibidas via webhook
-- =====================================================
CREATE TABLE IF NOT EXISTS noticias (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  medio_origen VARCHAR(50) NOT NULL,
  header VARCHAR(200) NOT NULL,
  title VARCHAR(300) NOT NULL,
  summary TEXT,
  content TEXT NOT NULL,
  images_url TEXT[],
  categories VARCHAR(100)[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para optimizar consultas
CREATE INDEX IF NOT EXISTS idx_noticias_medio_origen ON noticias(medio_origen);
CREATE INDEX IF NOT EXISTS idx_noticias_created_at ON noticias(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_noticias_categories ON noticias USING GIN(categories);

-- =====================================================
-- TABLA: medios
-- Configuración de medios de comunicación
-- =====================================================
CREATE TABLE IF NOT EXISTS medios (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  codigo VARCHAR(50) UNIQUE NOT NULL,
  nombre VARCHAR(200) NOT NULL,
  descripcion TEXT,
  activo BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices
CREATE INDEX IF NOT EXISTS idx_medios_codigo ON medios(codigo);
CREATE INDEX IF NOT EXISTS idx_medios_activo ON medios(activo);

-- =====================================================
-- TABLA: prompts_medios
-- Prompts personalizados por medio de comunicación
-- =====================================================
CREATE TABLE IF NOT EXISTS prompts_medios (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  medio_id UUID REFERENCES medios(id) ON DELETE CASCADE,
  tipo_prompt VARCHAR(50) NOT NULL, -- 'rewrite', 'summary', 'title'
  prompt_template TEXT NOT NULL,
  activo BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices
CREATE INDEX IF NOT EXISTS idx_prompts_medio_id ON prompts_medios(medio_id);
CREATE INDEX IF NOT EXISTS idx_prompts_tipo ON prompts_medios(tipo_prompt);
CREATE INDEX IF NOT EXISTS idx_prompts_activo ON prompts_medios(activo);

-- Constraint único para evitar duplicados
CREATE UNIQUE INDEX IF NOT EXISTS idx_prompts_medio_tipo_unique 
ON prompts_medios(medio_id, tipo_prompt) WHERE activo = true;

-- =====================================================
-- TABLA: system_logs
-- Logs del sistema almacenados en base de datos
-- =====================================================
CREATE TABLE IF NOT EXISTS system_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  level VARCHAR(20) NOT NULL, -- 'info', 'warn', 'error', 'debug'
  message TEXT NOT NULL,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_logs_metadata ON system_logs USING GIN(metadata);

-- =====================================================
-- TABLA: news_processing
-- Seguimiento del procesamiento de noticias
-- =====================================================
CREATE TABLE IF NOT EXISTS news_processing (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  noticia_id UUID REFERENCES noticias(id) ON DELETE CASCADE,
  status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  openai_tokens_used INTEGER,
  processing_time_ms INTEGER,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Índices
CREATE INDEX IF NOT EXISTS idx_news_processing_noticia_id ON news_processing(noticia_id);
CREATE INDEX IF NOT EXISTS idx_news_processing_status ON news_processing(status);
CREATE INDEX IF NOT EXISTS idx_news_processing_created_at ON news_processing(created_at DESC);

-- =====================================================
-- FUNCIONES Y TRIGGERS
-- =====================================================

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para updated_at
CREATE TRIGGER update_noticias_updated_at 
    BEFORE UPDATE ON noticias 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_medios_updated_at 
    BEFORE UPDATE ON medios 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_prompts_medios_updated_at 
    BEFORE UPDATE ON prompts_medios 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_news_processing_updated_at 
    BEFORE UPDATE ON news_processing 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- DATOS INICIALES
-- =====================================================

-- Insertar medios de ejemplo
INSERT INTO medios (codigo, nombre, descripcion) VALUES 
('test_medio', 'Medio de Prueba', 'Medio utilizado para testing del sistema'),
('diario_ejemplo', 'Diario Ejemplo', 'Diario de noticias generales'),
('tech_news', 'Tech News', 'Medio especializado en tecnología')
ON CONFLICT (codigo) DO NOTHING;

-- Insertar prompts por defecto
WITH medio_ids AS (
  SELECT id, codigo FROM medios WHERE codigo IN ('test_medio', 'diario_ejemplo', 'tech_news')
)
INSERT INTO prompts_medios (medio_id, tipo_prompt, prompt_template) 
SELECT 
  m.id,
  'rewrite',
  'Reescribe la siguiente noticia para el medio "' || m.codigo || '" manteniendo la información principal pero adaptando el estilo y tono. Noticia original: {content}'
FROM medio_ids m
ON CONFLICT DO NOTHING;

-- =====================================================
-- POLÍTICAS DE SEGURIDAD (RLS)
-- =====================================================

-- Habilitar RLS en todas las tablas
ALTER TABLE noticias ENABLE ROW LEVEL SECURITY;
ALTER TABLE medios ENABLE ROW LEVEL SECURITY;
ALTER TABLE prompts_medios ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE news_processing ENABLE ROW LEVEL SECURITY;

-- Política para permitir todas las operaciones con service role
CREATE POLICY "Service role can do everything" ON noticias
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can do everything" ON medios
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can do everything" ON prompts_medios
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can do everything" ON system_logs
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can do everything" ON news_processing
  FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- COMENTARIOS EN TABLAS
-- =====================================================

COMMENT ON TABLE noticias IS 'Almacena las noticias recibidas via webhook desde el panel de administración';
COMMENT ON TABLE medios IS 'Configuración de medios de comunicación y sus características';
COMMENT ON TABLE prompts_medios IS 'Prompts personalizados de OpenAI para cada medio de comunicación';
COMMENT ON TABLE system_logs IS 'Logs del sistema almacenados en base de datos para análisis';
COMMENT ON TABLE news_processing IS 'Seguimiento del estado de procesamiento de cada noticia';

-- =====================================================
-- VERIFICACIÓN FINAL
-- =====================================================

-- Mostrar resumen de tablas creadas
SELECT 
  schemaname,
  tablename,
  tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('noticias', 'medios', 'prompts_medios', 'system_logs', 'news_processing')
ORDER BY tablename;
