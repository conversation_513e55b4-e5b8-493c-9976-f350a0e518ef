const { getSupabaseClient, handleSupabaseError } = require('../config/database');
const { logger, logError } = require('../utils/logger');

class DatabaseService {
  constructor() {
    this.client = null;
  }

  // Inicializar cliente
  initialize(useServiceRole = true) {
    try {
      this.client = getSupabaseClient(useServiceRole);
      return true;
    } catch (error) {
      logError(error, { context: 'DatabaseService.initialize' });
      return false;
    }
  }

  // ==================== OPERACIONES DE NOTICIAS ====================

  // Crear nueva noticia
  async createNoticia(noticiaData) {
    try {
      if (!this.client) this.initialize();

      logger.info('Intentando crear noticia en Supabase', {
        medio_origen: noticiaData.medio_origen,
        title: noticiaData.title
      });

      const insertData = {
        medio_origen: noticiaData.medio_origen,
        header: noticiaData.header,
        title: noticiaData.title,
        summary: noticiaData.summary || null,
        content: noticiaData.content,
        images_url: noticiaData.images_url || null,
        categories: noticiaData.categories || null,
        created_at: new Date().toISOString()
      };

      logger.info('Datos a insertar', { insertData });

      const { data, error } = await this.client
        .from('noticias')
        .insert([insertData])
        .select()
        .single();

      if (error) {
        logger.error('Error de Supabase al insertar noticia', {
          error: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        });
        throw error;
      }

      logger.info('Noticia creada en base de datos', {
        id: data.id,
        medio_origen: data.medio_origen,
        title: data.title
      });

      return { success: true, data };
    } catch (error) {
      logger.error('Error en createNoticia', {
        error: error.message,
        stack: error.stack,
        noticiaData: {
          medio_origen: noticiaData.medio_origen,
          title: noticiaData.title
        }
      });

      const mappedError = handleSupabaseError(error, 'createNoticia');
      return { success: false, error: mappedError };
    }
  }

  // Obtener noticia por ID
  async getNoticiaById(id) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('noticias')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'getNoticiaById');
      return { success: false, error: mappedError };
    }
  }

  // Obtener noticias por medio
  async getNoticiasByMedio(medioOrigen, limit = 50, offset = 0) {
    try {
      if (!this.client) this.initialize();

      const { data, error, count } = await this.client
        .from('noticias')
        .select('*', { count: 'exact' })
        .eq('medio_origen', medioOrigen)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw error;
      }

      return { 
        success: true, 
        data: {
          noticias: data,
          total: count,
          limit,
          offset
        }
      };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'getNoticiasByMedio');
      return { success: false, error: mappedError };
    }
  }

  // Actualizar noticia
  async updateNoticia(id, updates) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('noticias')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info('Noticia actualizada', {
        id: data.id,
        updates: Object.keys(updates)
      });

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'updateNoticia');
      return { success: false, error: mappedError };
    }
  }

  // ==================== OPERACIONES DE MEDIOS ====================

  // Crear medio
  async createMedio(medioData) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('medios')
        .insert([{
          codigo: medioData.codigo,
          nombre: medioData.nombre,
          descripcion: medioData.descripcion || null,
          activo: medioData.activo !== undefined ? medioData.activo : true
        }])
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info('Medio creado', {
        id: data.id,
        codigo: data.codigo,
        nombre: data.nombre
      });

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'createMedio');
      return { success: false, error: mappedError };
    }
  }

  // Obtener medio por código
  async getMedioByCodigo(codigo) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('medios')
        .select('*')
        .eq('codigo', codigo)
        .eq('activo', true)
        .single();

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'getMedioByCodigo');
      return { success: false, error: mappedError };
    }
  }

  // Obtener todos los medios activos
  async getMediosActivos() {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('medios')
        .select('*')
        .eq('activo', true)
        .order('nombre');

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'getMediosActivos');
      return { success: false, error: mappedError };
    }
  }

  // ==================== OPERACIONES DE LOGS ====================

  // Crear log del sistema
  async createSystemLog(level, message, metadata = {}) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('system_logs')
        .insert([{
          level,
          message,
          metadata,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      // No logear errores de logging para evitar recursión
      return { success: false, error: { message: error.message } };
    }
  }

  // ==================== OPERACIONES DE PROCESAMIENTO ====================

  // Crear registro de procesamiento
  async createNewsProcessing(noticiaId, status = 'pending') {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('news_processing')
        .insert([{
          noticia_id: noticiaId,
          status,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info('Registro de procesamiento creado', {
        id: data.id,
        noticia_id: noticiaId,
        status
      });

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'createNewsProcessing');
      return { success: false, error: mappedError };
    }
  }

  // Actualizar estado de procesamiento
  async updateNewsProcessing(id, updates) {
    try {
      if (!this.client) this.initialize();

      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      if (updates.status === 'completed' || updates.status === 'failed') {
        updateData.completed_at = new Date().toISOString();
      }

      const { data, error } = await this.client
        .from('news_processing')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info('Estado de procesamiento actualizado', {
        id: data.id,
        status: data.status
      });

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'updateNewsProcessing');
      return { success: false, error: mappedError };
    }
  }

  // Obtener estado de procesamiento por noticia
  async getProcessingByNoticiaId(noticiaId) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('news_processing')
        .select('*')
        .eq('noticia_id', noticiaId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'getProcessingByNoticiaId');
      return { success: false, error: mappedError };
    }
  }
}

// Exportar instancia singleton
const databaseService = new DatabaseService();

module.exports = databaseService;
