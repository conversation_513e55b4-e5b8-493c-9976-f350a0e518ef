const { getSupabaseClient, handleSupabaseError } = require('../config/database');
const { logger, logError } = require('../utils/logger');

class DatabaseService {
  constructor() {
    this.client = null;
  }

  // Inicializar cliente
  initialize(useServiceRole = true) {
    try {
      this.client = getSupabaseClient(useServiceRole);
      return true;
    } catch (error) {
      logError(error, { context: 'DatabaseService.initialize' });
      return false;
    }
  }

  // ==================== OPERACIONES DE DIARIOS ====================

  // Crear nueva noticia
  async createDiario(diarioData) {
    try {
      if (!this.client) this.initialize();

      logger.info('Intentando crear diario en Supabase', {
        medio_origen: diarioData.medio_origen,
        title: diarioData.title
      });

      const insertData = {
        medio_origen: diarioData.medio_origen,
        header: diarioData.header,
        title: diarioData.title,
        summary: diarioData.summary || null,
        content: diarioData.content,
        images_url: diarioData.images_url || null,
        categories: diarioData.categories || null,
        created_at: new Date().toISOString()
      };

      logger.info('Datos a insertar', { insertData });

      const { data, error } = await this.client
        .from('diarios')
        .insert([insertData])
        .select()
        .single();

      if (error) {
        logger.error('Error de Supabase al insertar diario', {
          error: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint,
          fullError: JSON.stringify(error, null, 2)
        });
        throw error;
      }

      logger.info('Diario creado en base de datos', {
        id: data.id,
        medio_origen: data.medio_origen,
        title: data.title
      });

      return { success: true, data };
    } catch (error) {
      logger.error('Error en createDiario', {
        error: error.message,
        stack: error.stack,
        diarioData: {
          medio_origen: diarioData.medio_origen,
          title: diarioData.title
        }
      });

      const mappedError = handleSupabaseError(error, 'createDiario');
      return { success: false, error: mappedError };
    }
  }

  // Obtener diario por ID
  async getDiarioById(id) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('diarios')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'getDiarioById');
      return { success: false, error: mappedError };
    }
  }

  // Obtener diarios por medio
  async getDiariosByMedio(medioOrigen, limit = 50, offset = 0) {
    try {
      if (!this.client) this.initialize();

      const { data, error, count } = await this.client
        .from('diarios')
        .select('*', { count: 'exact' })
        .eq('medio_origen', medioOrigen)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw error;
      }

      return {
        success: true,
        data: {
          diarios: data,
          total: count,
          limit,
          offset
        }
      };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'getDiariosByMedio');
      return { success: false, error: mappedError };
    }
  }

  // Actualizar diario
  async updateDiario(id, updates) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('diarios')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info('Diario actualizado', {
        id: data.id,
        updates: Object.keys(updates)
      });

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'updateDiario');
      return { success: false, error: mappedError };
    }
  }

  // ==================== OPERACIONES DE MEDIOS ====================

  // Crear medio
  async createMedio(medioData) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('medios')
        .insert([{
          codigo: medioData.codigo,
          nombre: medioData.nombre,
          descripcion: medioData.descripcion || null,
          activo: medioData.activo !== undefined ? medioData.activo : true
        }])
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info('Medio creado', {
        id: data.id,
        codigo: data.codigo,
        nombre: data.nombre
      });

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'createMedio');
      return { success: false, error: mappedError };
    }
  }

  // Obtener medio por código
  async getMedioByCodigo(codigo) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('medios')
        .select('*')
        .eq('codigo', codigo)
        .eq('activo', true)
        .single();

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'getMedioByCodigo');
      return { success: false, error: mappedError };
    }
  }

  // Obtener todos los medios activos
  async getMediosActivos() {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('medios')
        .select('*')
        .eq('activo', true)
        .order('nombre');

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'getMediosActivos');
      return { success: false, error: mappedError };
    }
  }

  // ==================== OPERACIONES DE LOGS ====================

  // Crear log del sistema
  async createSystemLog(level, message, metadata = {}) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('system_logs')
        .insert([{
          level,
          message,
          metadata,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      // No logear errores de logging para evitar recursión
      return { success: false, error: { message: error.message } };
    }
  }

  // ==================== OPERACIONES DE PROCESAMIENTO ====================

  // Crear registro de procesamiento
  async createNewsProcessing(diarioId, status = 'pending') {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('news_processing')
        .insert([{
          diario_id: diarioId,
          status,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info('Registro de procesamiento creado', {
        id: data.id,
        diario_id: diarioId,
        status
      });

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'createNewsProcessing');
      return { success: false, error: mappedError };
    }
  }

  // Actualizar estado de procesamiento
  async updateNewsProcessing(id, updates) {
    try {
      if (!this.client) this.initialize();

      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      if (updates.status === 'completed' || updates.status === 'failed') {
        updateData.completed_at = new Date().toISOString();
      }

      const { data, error } = await this.client
        .from('news_processing')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info('Estado de procesamiento actualizado', {
        id: data.id,
        status: data.status
      });

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'updateNewsProcessing');
      return { success: false, error: mappedError };
    }
  }

  // Obtener estado de procesamiento por diario
  async getProcessingByDiarioId(diarioId) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('news_processing')
        .select('*')
        .eq('diario_id', diarioId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      const mappedError = handleSupabaseError(error, 'getProcessingByDiarioId');
      return { success: false, error: mappedError };
    }
  }
}

// Exportar instancia singleton
const databaseService = new DatabaseService();

module.exports = databaseService;
