// Test simple de conexión a Supabase
const { getSupabaseClient } = require('./src/config/database');

async function testSupabaseSimple() {
  console.log('🧪 Test Simple de Supabase\n');
  
  try {
    const client = getSupabaseClient(true);
    
    console.log('1. 🔍 Probando conexión básica...');
    
    // Test 1: Listar tablas del esquema público
    console.log('   📋 Listando tablas en esquema público...');
    const { data: tables, error: tablesError } = await client
      .rpc('exec_sql', { 
        sql_query: "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;" 
      });
    
    if (tablesError) {
      console.log('   ⚠️  Error listando tablas:', tablesError.message);
      
      // Test alternativo: intentar crear una tabla simple
      console.log('   🔧 Intentando crear tabla de prueba...');
      const { data: createData, error: createError } = await client
        .rpc('exec_sql', { 
          sql_query: "CREATE TABLE IF NOT EXISTS test_connection (id SERIAL PRIMARY KEY, name TEXT);" 
        });
      
      if (createError) {
        console.log('   ❌ Error creando tabla de prueba:', createError.message);
      } else {
        console.log('   ✅ Tabla de prueba creada exitosamente');
        
        // Limpiar tabla de prueba
        await client.rpc('exec_sql', { 
          sql_query: "DROP TABLE IF EXISTS test_connection;" 
        });
        console.log('   🧹 Tabla de prueba eliminada');
      }
    } else {
      console.log('   ✅ Tablas encontradas:', tables);
    }
    
    console.log('\n2. 🔍 Probando acceso directo a tablas...');
    
    // Test 2: Intentar acceso directo a tabla noticias
    console.log('   📰 Intentando acceder a tabla noticias...');
    const { data: noticiasData, error: noticiasError } = await client
      .from('noticias')
      .select('count', { count: 'exact', head: true });
    
    if (noticiasError) {
      console.log('   ❌ Error accediendo a noticias:', noticiasError.message);
      console.log('   📊 Código de error:', noticiasError.code);
      console.log('   💡 Detalles:', noticiasError.details);
    } else {
      console.log('   ✅ Acceso a tabla noticias exitoso');
    }
    
    // Test 3: Intentar acceso a tabla medios
    console.log('   🏢 Intentando acceder a tabla medios...');
    const { data: mediosData, error: mediosError } = await client
      .from('medios')
      .select('count', { count: 'exact', head: true });
    
    if (mediosError) {
      console.log('   ❌ Error accediendo a medios:', mediosError.message);
      console.log('   📊 Código de error:', mediosError.code);
      console.log('   💡 Detalles:', mediosError.details);
    } else {
      console.log('   ✅ Acceso a tabla medios exitoso');
    }
    
    console.log('\n3. 🔍 Información de autenticación...');
    
    // Test 4: Verificar rol actual
    console.log('   👤 Verificando rol de usuario...');
    const { data: roleData, error: roleError } = await client
      .rpc('exec_sql', { 
        sql_query: "SELECT current_user, session_user, current_role;" 
      });
    
    if (roleError) {
      console.log('   ⚠️  Error obteniendo rol:', roleError.message);
    } else {
      console.log('   ✅ Información de usuario:', roleData);
    }
    
    console.log('\n📋 Resumen del Test:');
    console.log('- Conexión a Supabase: ✅ Exitosa');
    console.log('- Acceso a tablas:', noticiasError || mediosError ? '❌ Con problemas' : '✅ Exitoso');
    console.log('- Posibles causas de problemas:');
    console.log('  • Las tablas no están creadas en el esquema público');
    console.log('  • Problemas de permisos/RLS');
    console.log('  • Service role key incorrecta');
    
    if (noticiasError || mediosError) {
      console.log('\n💡 Solución recomendada:');
      console.log('1. Ejecutar el SQL en create-tables.sql en el dashboard de Supabase');
      console.log('2. Verificar que las tablas se crean en el esquema "public"');
      console.log('3. Verificar que las políticas RLS permiten acceso al service role');
    }
    
  } catch (error) {
    console.error('\n💥 Error general:', error.message);
  }
}

// Ejecutar test
if (require.main === module) {
  testSupabaseSimple();
}

module.exports = { testSupabaseSimple };
