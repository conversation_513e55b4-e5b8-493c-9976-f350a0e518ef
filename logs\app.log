2025-07-17 01:50:51 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 01:51:15 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:51:15 [WARN]: Ruta no encontrada
Metadata: {
  "method": "GET",
  "url": "/",
  "ip": "::1"
}
2025-07-17 01:51:44 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 01:51:58 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:51:58 [WARN]: Ruta no encontrada
Metadata: {
  "method": "GET",
  "url": "/",
  "ip": "::1"
}
2025-07-17 01:52:36 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 01:52:50 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:52:56 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:53:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 01:53:35 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 01:53:35 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 01:53:35 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 01:53:35 [INFO]: Noticia recibida y preparada para procesamiento
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
  "content_length": 364,
  "has_images": true,
  "categories_count": 3
}
2025-07-17 01:53:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 01:53:35 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 01:53:35 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 01:53:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 01:53:35 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:00:34 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:00:34 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:02:04 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:02:04 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:02:04 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:02:20 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:02:20 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "TypeError: fetch failed",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:02:20 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 0,
  "tables": []
}
2025-07-17 02:02:27 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/ready",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:02:27 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "TypeError: fetch failed",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:02:27 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 0,
  "tables": []
}
2025-07-17 02:04:01 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:04:01 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:04:01 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:04:17 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:04:17 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:05:16 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:05:16 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:05:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.information_schema.tables\" does not exist",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:06:14 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:06:14 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:06:14 [INFO]: Conexión con Supabase verificada correctamente
2025-07-17 02:06:17 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 5,
  "tables": [
    "noticias",
    "medios",
    "prompts_medios",
    "system_logs",
    "news_processing"
  ]
}
2025-07-17 02:06:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.medios\" does not exist",
  "context": {
    "context": "getMediosActivos",
    "code": "42P01",
    "message": "relation \"public.medios\" does not exist",
    "details": null,
    "hint": null
  }
}
2025-07-17 02:06:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:06:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:06:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:07:18 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:07:19 [INFO]: Conexión con Supabase verificada correctamente
2025-07-17 02:07:22 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 5,
  "tables": [
    "noticias",
    "medios",
    "prompts_medios",
    "system_logs",
    "news_processing"
  ]
}
2025-07-17 02:08:05 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:08:05 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:08:15 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:08:15 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:08:15 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:08:15 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:08:15 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:08:16 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createNoticia"
  }
}
2025-07-17 02:08:16 [ERROR]: Error en el sistema
Error: Error guardando noticia en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando noticia en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:08:16 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:08:16 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:08:16 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:08:16 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:08:16 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:08:57 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:08:57 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:08:57 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
