2025-07-17 01:50:51 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 01:51:15 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:51:15 [WARN]: Ruta no encontrada
Metadata: {
  "method": "GET",
  "url": "/",
  "ip": "::1"
}
2025-07-17 01:51:44 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 01:51:58 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:51:58 [WARN]: Ruta no encontrada
Metadata: {
  "method": "GET",
  "url": "/",
  "ip": "::1"
}
2025-07-17 01:52:36 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 01:52:50 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:52:56 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:53:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 01:53:35 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 01:53:35 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 01:53:35 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 01:53:35 [INFO]: Noticia recibida y preparada para procesamiento
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
  "content_length": 364,
  "has_images": true,
  "categories_count": 3
}
2025-07-17 01:53:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 01:53:35 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 01:53:35 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 01:53:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 01:53:35 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:00:34 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:00:34 [INFO]: Cliente Supabase Anon inicializado correctamente
