2025-07-17 01:50:51 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 01:51:15 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:51:15 [WARN]: Ruta no encontrada
Metadata: {
  "method": "GET",
  "url": "/",
  "ip": "::1"
}
2025-07-17 01:51:44 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 01:51:58 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:51:58 [WARN]: Ruta no encontrada
Metadata: {
  "method": "GET",
  "url": "/",
  "ip": "::1"
}
2025-07-17 01:52:36 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 01:52:50 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:52:56 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:53:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 01:53:35 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 01:53:35 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 01:53:35 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 01:53:35 [INFO]: Noticia recibida y preparada para procesamiento
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
  "content_length": 364,
  "has_images": true,
  "categories_count": 3
}
2025-07-17 01:53:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 01:53:35 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 01:53:35 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 01:53:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 01:53:35 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:00:34 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:00:34 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:02:04 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:02:04 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:02:04 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:02:20 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:02:20 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "TypeError: fetch failed",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:02:20 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 0,
  "tables": []
}
2025-07-17 02:02:27 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/ready",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:02:27 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "TypeError: fetch failed",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:02:27 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 0,
  "tables": []
}
2025-07-17 02:04:01 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:04:01 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:04:01 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:04:17 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:04:17 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:05:16 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:05:16 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:05:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.information_schema.tables\" does not exist",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:06:14 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:06:14 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:06:14 [INFO]: Conexión con Supabase verificada correctamente
2025-07-17 02:06:17 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 5,
  "tables": [
    "noticias",
    "medios",
    "prompts_medios",
    "system_logs",
    "news_processing"
  ]
}
2025-07-17 02:06:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.medios\" does not exist",
  "context": {
    "context": "getMediosActivos",
    "code": "42P01",
    "message": "relation \"public.medios\" does not exist",
    "details": null,
    "hint": null
  }
}
2025-07-17 02:06:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:06:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:06:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:07:18 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:07:19 [INFO]: Conexión con Supabase verificada correctamente
2025-07-17 02:07:22 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 5,
  "tables": [
    "noticias",
    "medios",
    "prompts_medios",
    "system_logs",
    "news_processing"
  ]
}
2025-07-17 02:08:05 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:08:05 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:08:15 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:08:15 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:08:15 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:08:15 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:08:15 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:08:16 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createNoticia"
  }
}
2025-07-17 02:08:16 [ERROR]: Error en el sistema
Error: Error guardando noticia en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando noticia en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:08:16 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:08:16 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:08:16 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:08:16 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:08:16 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:08:57 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:08:57 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:08:57 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:24:19 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:24:19 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:24:19 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:24:38 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:24:38 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:24:48 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:24:49 [INFO]: Conexión con Supabase verificada correctamente
2025-07-17 02:24:52 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 5,
  "tables": [
    "diarios",
    "medios",
    "prompts_medios",
    "system_logs",
    "news_processing"
  ]
}
2025-07-17 02:25:00 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:25:00 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:25:00 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:25:00 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:25:00 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:25:00 [INFO]: Intentando crear diario en Supabase
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:25:00 [INFO]: Datos a insertar
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de procesamiento automatizado que utiliza inteligencia artificial para reescribir noticias según diferentes formatos de medios de comunicación. Este sistema integra OpenAI API con Supabase para almacenar tanto las versiones originales como las reescritas de las noticias.",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ],
    "created_at": "2025-07-17T05:25:00.334Z"
  }
}
2025-07-17 02:25:00 [ERROR]: Error de Supabase al insertar diario
Metadata: {}
2025-07-17 02:25:00 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:25:00 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:25:00 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:25:00 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:25:00 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:25:00 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:25:00 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:25:00 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:30:05 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/ready",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:30:06 [INFO]: Conexión con Supabase verificada correctamente
2025-07-17 02:30:09 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 5,
  "tables": [
    "diarios",
    "medios",
    "prompts_medios",
    "system_logs",
    "news_processing"
  ]
}
2025-07-17 02:30:19 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:30:19 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:30:19 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:30:19 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:30:19 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:30:19 [INFO]: Intentando crear diario en Supabase
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:30:19 [INFO]: Datos a insertar
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de procesamiento automatizado que utiliza inteligencia artificial para reescribir noticias según diferentes formatos de medios de comunicación. Este sistema integra OpenAI API con Supabase para almacenar tanto las versiones originales como las reescritas de las noticias.",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ],
    "created_at": "2025-07-17T05:30:19.170Z"
  }
}
2025-07-17 02:30:19 [ERROR]: Error de Supabase al insertar diario
Metadata: {}
2025-07-17 02:30:19 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:30:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:30:19 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:30:19 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:30:19 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:30:19 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:30:19 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:30:19 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:30:58 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:30:58 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:30:58 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:31:22 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:31:22 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:31:22 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:31:22 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:31:22 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:31:22 [INFO]: Intentando crear diario en Supabase
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:31:22 [INFO]: Datos a insertar
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de procesamiento automatizado que utiliza inteligencia artificial para reescribir noticias según diferentes formatos de medios de comunicación. Este sistema integra OpenAI API con Supabase para almacenar tanto las versiones originales como las reescritas de las noticias.",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ],
    "created_at": "2025-07-17T05:31:22.082Z"
  }
}
2025-07-17 02:31:22 [ERROR]: Error de Supabase al insertar diario
Metadata: {
  "fullError": "{}"
}
2025-07-17 02:31:22 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:31:22 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:31:22 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:31:22 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:31:22 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:31:22 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:31:22 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:31:22 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:32:10 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:32:10 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:35:30 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:35:30 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:35:30 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:35:30 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:35:30 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:35:30 [INFO]: Intentando crear diario en Supabase
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:35:30 [INFO]: Datos a insertar
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de procesamiento automatizado que utiliza inteligencia artificial para reescribir noticias según diferentes formatos de medios de comunicación. Este sistema integra OpenAI API con Supabase para almacenar tanto las versiones originales como las reescritas de las noticias.",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ],
    "created_at": "2025-07-17T05:35:30.570Z"
  }
}
2025-07-17 02:35:31 [ERROR]: Error de Supabase al insertar diario
Metadata: {
  "fullError": "{}"
}
2025-07-17 02:35:31 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:35:31 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:35:31 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:35:31 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:35:31 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:35:31 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:35:31 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:35:31 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:35:53 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:35:53 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:36:36 [INFO]: SIGINT recibido, cerrando servidor...
2025-07-17 02:41:13 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:41:13 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:41:13 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:41:30 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:41:30 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:43:19 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:43:19 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:43:19 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:43:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:43:35 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:43:35 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:43:35 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:43:35 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:43:35 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:43:35 [INFO]: Creando diario con SQL directo
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:43:35 [INFO]: Ejecutando SQL de inserción
Metadata: {
  "sql": "\n        INSERT INTO diarios (\n          medio_origen, \n          header, \n          title, \n          summary, \n          content, \n          images_url, \n          categories,\n          created_at,\n...",
  "dataLength": 829
}
2025-07-17 02:43:36 [ERROR]: Error en SQL directo
Metadata: {
  "error": "Could not find the function public.exec_sql(sql_query) in the schema cache",
  "code": "PGRST202",
  "details": "Searched for the function public.exec_sql with parameter sql_query or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.",
  "hint": null
}
2025-07-17 02:43:36 [ERROR]: Error en createDiario híbrido
Metadata: {
  "error": "Could not find the function public.exec_sql(sql_query) in the schema cache",
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:43:36 [ERROR]: Error en servicio híbrido
Metadata: {
  "error": {
    "code": "HYBRID_SQL_ERROR",
    "message": "Could not find the function public.exec_sql(sql_query) in the schema cache",
    "originalError": {
      "code": "PGRST202",
      "details": "Searched for the function public.exec_sql with parameter sql_query or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.",
      "hint": null,
      "message": "Could not find the function public.exec_sql(sql_query) in the schema cache"
    }
  },
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:43:36 [ERROR]: Error en createDiario
Error: Could not find the function public.exec_sql(sql_query) in the schema cache
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "error": "Could not find the function public.exec_sql(sql_query) in the schema cache",
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:43:36 [ERROR]: Error en el sistema
Error: Could not find the function public.exec_sql(sql_query) in the schema cache
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "type": "system_error",
  "error": "Could not find the function public.exec_sql(sql_query) in the schema cache",
  "context": {
    "context": "createDiario",
    "message": "Could not find the function public.exec_sql(sql_query) in the schema cache"
  }
}
2025-07-17 02:43:36 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "message": "Could not find the function public.exec_sql(sql_query) in the schema cache",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:43:36 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:43:36 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:43:36 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:43:36 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:43:36 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:44:46 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:44:46 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:44:46 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:45:06 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:45:06 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:45:06 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:45:06 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:45:06 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:45:06 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:45:06 [INFO]: Creando diario con cliente optimizado
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:45:06 [INFO]: Datos preparados para inserción
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de proc...",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ]
  }
}
2025-07-17 02:45:07 [WARN]: Inserción estándar falló, intentando alternativa
Metadata: {}
2025-07-17 02:45:07 [INFO]: Intentando inserción sin select
2025-07-17 02:45:07 [ERROR]: Error en createDiario híbrido
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:45:07 [ERROR]: Error en servicio híbrido
Metadata: {
  "error": {
    "code": "HYBRID_INSERT_ERROR",
    "originalError": {}
  },
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:45:07 [ERROR]: Error en createDiario
Error
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "error": "",
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:45:07 [ERROR]: Error en el sistema
Error
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "type": "system_error",
  "error": "",
  "context": {
    "context": "createDiario",
    "message": ""
  }
}
2025-07-17 02:45:07 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "message": "",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:45:07 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:45:07 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:45:07 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:45:07 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:45:07 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:45:58 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:45:58 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:45:58 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:46:19 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:46:19 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:46:19 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:46:19 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:46:19 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:46:19 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:46:19 [INFO]: Creando diario con cliente optimizado
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:46:19 [INFO]: Datos preparados para inserción
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de proc...",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ]
  }
}
2025-07-17 02:46:20 [WARN]: Inserción estándar falló, intentando alternativa
Metadata: {
  "fullError": "{}"
}
2025-07-17 02:46:20 [INFO]: Intentando inserción sin select
2025-07-17 02:46:20 [ERROR]: Inserción sin select también falló
Metadata: {
  "fullError": "{}"
}
2025-07-17 02:46:20 [ERROR]: Error en createDiario híbrido
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:46:20 [ERROR]: Error en servicio híbrido
Metadata: {
  "error": {
    "code": "HYBRID_INSERT_ERROR",
    "originalError": {}
  },
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:46:20 [ERROR]: Error en createDiario
Error
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "error": "",
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:46:20 [ERROR]: Error en el sistema
Error
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "type": "system_error",
  "error": "",
  "context": {
    "context": "createDiario",
    "message": ""
  }
}
2025-07-17 02:46:20 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "message": "",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:46:20 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:46:20 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:46:20 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:46:20 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:46:20 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:50:02 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:50:02 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:50:02 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:50:36 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:50:36 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:50:36 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:50:36 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:50:36 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:50:36 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:50:36 [INFO]: Creando diario con cliente optimizado
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:50:36 [INFO]: Datos preparados para inserción
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de proc...",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ]
  }
}
2025-07-17 02:50:36 [INFO]: Diario creado exitosamente
Metadata: {
  "id": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:50:36 [INFO]: Diario creado exitosamente con servicio híbrido
Metadata: {
  "id": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:50:36 [INFO]: Procesamiento de noticia database_save_completed
Metadata: {
  "type": "news_processing",
  "newsId": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "status": "database_save_completed"
}
2025-07-17 02:50:37 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "b3a4ef5c-9c9f-4ef6-917d-64617c3d28bf",
  "diario_id": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "status": "pending"
}
2025-07-17 02:50:37 [INFO]: Diario guardado y preparado para procesamiento
Metadata: {
  "id": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
  "content_length": 364,
  "has_images": true,
  "categories_count": 3,
  "processing_id": "b3a4ef5c-9c9f-4ef6-917d-64617c3d28bf"
}
2025-07-17 02:50:37 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:50:37 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:50:37 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:50:37 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:50:37 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:50:48 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:50:49 [INFO]: Conexión con Supabase verificada correctamente
2025-07-17 02:50:52 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 5,
  "tables": [
    "diarios",
    "medios",
    "prompts_medios",
    "system_logs",
    "news_processing"
  ]
}
2025-07-17 02:54:01 [WARN]: OpenAI API key no configurada o es temporal
Metadata: {
  "hasKey": true,
  "isTemp": true
}
2025-07-17 02:55:44 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:55:44 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:55:44 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_medio"
}
2025-07-17 02:55:44 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 02:55:44 [INFO]: Guardando prompt personalizado
Metadata: {
  "medio": "test_medio_custom",
  "hasTemplate": true
}
2025-07-17 02:55:45 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "PromptService.savePromptForMedio",
    "medio": "test_medio_custom"
  }
}
2025-07-17 02:55:45 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_medio_custom"
}
2025-07-17 02:55:45 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio_custom"
  }
}
2025-07-17 02:55:46 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getAllActivePrompts"
  }
}
2025-07-17 02:55:46 [INFO]: Cache de prompts limpiado
2025-07-17 02:55:46 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_medio"
}
2025-07-17 02:55:46 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 02:55:57 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:55:57 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:58:02 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:58:02 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:58:02 [INFO]: Cliente OpenAI inicializado correctamente
Metadata: {
  "model": "gpt-3.5-turbo",
  "maxTokens": 2000
}
2025-07-17 02:58:02 [INFO]: Servicios de procesamiento inicializados
Metadata: {
  "openai": true,
  "prompts": true,
  "database": true
}
2025-07-17 02:58:05 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "\"failed to parse logic tree ((news_processing.status.is.null,news_processing.status.eq.pending))\" (line 1, column 20)",
  "context": {
    "context": "ProcessingService.getPendingNews"
  }
}
2025-07-17 02:58:12 [INFO]: Estadísticas de procesamiento reseteadas
2025-07-17 03:01:30 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 03:01:30 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 03:01:30 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 03:01:50 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 03:01:50 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:01:50 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:01:50 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:01:50 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 03:01:50 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:01:50 [INFO]: Creando diario con cliente optimizado
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:01:50 [INFO]: Datos preparados para inserción
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de proc...",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ]
  }
}
2025-07-17 03:01:51 [INFO]: Diario creado exitosamente
Metadata: {
  "id": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:01:51 [INFO]: Diario creado exitosamente con servicio híbrido
Metadata: {
  "id": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:01:51 [INFO]: Procesamiento de noticia database_save_completed
Metadata: {
  "type": "news_processing",
  "newsId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "status": "database_save_completed"
}
2025-07-17 03:01:51 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "b93218aa-3836-4200-a000-2aea5be43948",
  "diario_id": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "status": "pending"
}
2025-07-17 03:01:51 [INFO]: Diario guardado y preparado para procesamiento
Metadata: {
  "id": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
  "content_length": 364,
  "has_images": true,
  "categories_count": 3,
  "processing_id": "b93218aa-3836-4200-a000-2aea5be43948"
}
2025-07-17 03:01:51 [INFO]: Procesamiento de noticia starting_openai_processing
Metadata: {
  "type": "news_processing",
  "newsId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "status": "starting_openai_processing"
}
2025-07-17 03:01:51 [INFO]: Iniciando procesamiento automático en background
Metadata: {
  "diarioId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "medio": "test_medio"
}
2025-07-17 03:01:51 [INFO]: Iniciando procesamiento de noticia
Metadata: {
  "processingId": "proc_1752732111989_t16g16n1p",
  "newsId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "medio": "test_medio",
  "options": {
    "background": true,
    "priority": "normal"
  }
}
2025-07-17 03:01:51 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 03:01:51 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:01:51 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 03:01:51 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 03:01:51 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:01:52 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "e474002f-5901-4f4e-a1fc-7693d4aa4110",
  "diario_id": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "status": "processing"
}
2025-07-17 03:01:52 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_medio"
}
2025-07-17 03:01:52 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 03:01:52 [INFO]: Prompt obtenido para procesamiento
Metadata: {
  "processingId": "proc_1752732111989_t16g16n1p",
  "isCustom": false,
  "medio": "test_medio"
}
2025-07-17 03:01:52 [ERROR]: Error en el sistema
Error: OpenAI service no está disponible
    at OpenAIService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\openaiService.js:77:15)
    at ProcessingService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\processingService.js:77:48)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Immediate.<anonymous> (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:83:34)
Metadata: {
  "type": "system_error",
  "error": "OpenAI service no está disponible",
  "context": {
    "context": "OpenAIService.processNews",
    "newsId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
    "medio": "test_medio"
  }
}
2025-07-17 03:01:52 [ERROR]: Error en el sistema
Error: Error en OpenAI: OpenAI service no está disponible
    at ProcessingService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\processingService.js:84:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Immediate.<anonymous> (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:83:34)
Metadata: {
  "type": "system_error",
  "error": "Error en OpenAI: OpenAI service no está disponible",
  "context": {
    "context": "ProcessingService.processNews",
    "processingId": "proc_1752732111989_t16g16n1p",
    "newsId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
    "medio": "test_medio"
  }
}
2025-07-17 03:01:52 [ERROR]: Error en procesamiento automático
Metadata: {
  "diarioId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "error": "Error en OpenAI: OpenAI service no está disponible"
}
2025-07-17 03:12:22 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 03:12:22 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 03:12:22 [INFO]: Servicio de monitoreo inicializado
Metadata: {
  "startTime": "2025-07-17T06:12:22.620Z"
}
2025-07-17 03:12:22 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 03:12:44 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 03:12:44 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:12:44 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:12:44 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:12:44 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 03:12:44 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:12:44 [INFO]: Creando diario con cliente optimizado
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:12:44 [INFO]: Datos preparados para inserción
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de proc...",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ]
  }
}
2025-07-17 03:12:44 [INFO]: Diario creado exitosamente
Metadata: {
  "id": "1e78f979-2d26-4af4-9672-e186f92313e6",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:12:44 [INFO]: Diario creado exitosamente con servicio híbrido
Metadata: {
  "id": "1e78f979-2d26-4af4-9672-e186f92313e6",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:12:44 [INFO]: Procesamiento de noticia database_save_completed
Metadata: {
  "type": "news_processing",
  "newsId": "1e78f979-2d26-4af4-9672-e186f92313e6",
  "status": "database_save_completed"
}
2025-07-17 03:12:45 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "ab32da1d-29a6-4be6-81da-e3be6ff4e278",
  "diario_id": "1e78f979-2d26-4af4-9672-e186f92313e6",
  "status": "pending"
}
2025-07-17 03:12:45 [INFO]: Diario guardado y preparado para procesamiento
Metadata: {
  "id": "1e78f979-2d26-4af4-9672-e186f92313e6",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
  "content_length": 364,
  "has_images": true,
  "categories_count": 3,
  "processing_id": "ab32da1d-29a6-4be6-81da-e3be6ff4e278"
}
2025-07-17 03:12:45 [INFO]: Procesamiento de noticia starting_openai_processing
Metadata: {
  "type": "news_processing",
  "newsId": "1e78f979-2d26-4af4-9672-e186f92313e6",
  "status": "starting_openai_processing"
}
2025-07-17 03:12:45 [INFO]: Iniciando procesamiento automático en background
Metadata: {
  "diarioId": "1e78f979-2d26-4af4-9672-e186f92313e6",
  "medio": "test_medio"
}
2025-07-17 03:12:45 [INFO]: Iniciando procesamiento de noticia
Metadata: {
  "processingId": "proc_1752732765515_ueetpa26p",
  "newsId": "1e78f979-2d26-4af4-9672-e186f92313e6",
  "medio": "test_medio",
  "options": {
    "background": true,
    "priority": "normal"
  }
}
2025-07-17 03:12:45 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 03:12:45 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:12:45 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 03:12:45 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 03:12:45 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:12:45 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "75f75998-bf74-4c56-bd10-2b073eea8217",
  "diario_id": "1e78f979-2d26-4af4-9672-e186f92313e6",
  "status": "processing"
}
2025-07-17 03:12:45 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_medio"
}
2025-07-17 03:12:45 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 03:12:45 [INFO]: Prompt obtenido para procesamiento
Metadata: {
  "processingId": "proc_1752732765515_ueetpa26p",
  "isCustom": false,
  "medio": "test_medio"
}
2025-07-17 03:12:45 [ERROR]: Error en el sistema
Error: OpenAI service no está disponible
    at OpenAIService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\openaiService.js:77:15)
    at ProcessingService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\processingService.js:78:48)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Immediate.<anonymous> (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:84:34)
Metadata: {
  "type": "system_error",
  "error": "OpenAI service no está disponible",
  "context": {
    "context": "OpenAIService.processNews",
    "newsId": "1e78f979-2d26-4af4-9672-e186f92313e6",
    "medio": "test_medio"
  }
}
2025-07-17 03:12:45 [ERROR]: Error en el sistema
Error: Error en OpenAI: OpenAI service no está disponible
    at ProcessingService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\processingService.js:85:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Immediate.<anonymous> (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:84:34)
Metadata: {
  "type": "system_error",
  "error": "Error en OpenAI: OpenAI service no está disponible",
  "context": {
    "context": "ProcessingService.processNews",
    "processingId": "proc_1752732765515_ueetpa26p",
    "newsId": "1e78f979-2d26-4af4-9672-e186f92313e6",
    "medio": "test_medio"
  }
}
2025-07-17 03:12:45 [INFO]: Procesamiento registrado en métricas
Metadata: {
  "success": false,
  "tokensUsed": 0,
  "processingTime": 386,
  "totalProcessed": 1
}
2025-07-17 03:12:45 [ERROR]: Error en procesamiento automático
Metadata: {
  "diarioId": "1e78f979-2d26-4af4-9672-e186f92313e6",
  "error": "Error en OpenAI: OpenAI service no está disponible"
}
2025-07-17 03:13:01 [INFO]: Cliente OpenAI inicializado correctamente
Metadata: {
  "model": "gpt-3.5-turbo",
  "maxTokens": 2000
}
2025-07-17 03:13:02 [INFO]: Iniciando procesamiento con OpenAI
Metadata: {
  "newsId": "test-news-001",
  "medio": "test_medio",
  "titleLength": 60,
  "contentLength": 424
}
2025-07-17 03:13:02 [INFO]: Prompt construido
Metadata: {
  "promptLength": 1093,
  "newsId": "test-news-001"
}
2025-07-17 03:13:04 [INFO]: Procesamiento OpenAI completado
Metadata: {
  "newsId": "test-news-001",
  "tokensUsed": 540,
  "processingTime": 2032,
  "outputLength": 874,
  "model": "gpt-3.5-turbo-0125"
}
2025-07-17 03:13:26 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 03:13:26 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 03:13:26 [INFO]: Servicio de monitoreo inicializado
Metadata: {
  "startTime": "2025-07-17T06:13:26.144Z"
}
2025-07-17 03:13:26 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 03:13:44 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 03:13:44 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:13:44 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:13:44 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:13:44 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 03:13:44 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:13:44 [INFO]: Creando diario con cliente optimizado
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:13:44 [INFO]: Datos preparados para inserción
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de proc...",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ]
  }
}
2025-07-17 03:13:45 [INFO]: Diario creado exitosamente
Metadata: {
  "id": "72cf6839-98be-4f03-93d3-1e5a1b33d12e",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:13:45 [INFO]: Diario creado exitosamente con servicio híbrido
Metadata: {
  "id": "72cf6839-98be-4f03-93d3-1e5a1b33d12e",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:13:45 [INFO]: Procesamiento de noticia database_save_completed
Metadata: {
  "type": "news_processing",
  "newsId": "72cf6839-98be-4f03-93d3-1e5a1b33d12e",
  "status": "database_save_completed"
}
2025-07-17 03:13:45 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "bfd83534-1e06-4f9b-b299-adc29edbfb27",
  "diario_id": "72cf6839-98be-4f03-93d3-1e5a1b33d12e",
  "status": "pending"
}
2025-07-17 03:13:45 [INFO]: Diario guardado y preparado para procesamiento
Metadata: {
  "id": "72cf6839-98be-4f03-93d3-1e5a1b33d12e",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
  "content_length": 364,
  "has_images": true,
  "categories_count": 3,
  "processing_id": "bfd83534-1e06-4f9b-b299-adc29edbfb27"
}
2025-07-17 03:13:45 [INFO]: Procesamiento de noticia starting_openai_processing
Metadata: {
  "type": "news_processing",
  "newsId": "72cf6839-98be-4f03-93d3-1e5a1b33d12e",
  "status": "starting_openai_processing"
}
2025-07-17 03:13:45 [INFO]: Iniciando procesamiento automático en background
Metadata: {
  "diarioId": "72cf6839-98be-4f03-93d3-1e5a1b33d12e",
  "medio": "test_medio"
}
2025-07-17 03:13:45 [INFO]: Iniciando procesamiento de noticia
Metadata: {
  "processingId": "proc_1752732825643_ajj80lm69",
  "newsId": "72cf6839-98be-4f03-93d3-1e5a1b33d12e",
  "medio": "test_medio",
  "options": {
    "background": true,
    "priority": "normal"
  }
}
2025-07-17 03:13:45 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 03:13:45 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:13:45 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 03:13:45 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 03:13:45 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:13:45 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "51e4c392-50d5-409f-af2b-a6a4606cea2a",
  "diario_id": "72cf6839-98be-4f03-93d3-1e5a1b33d12e",
  "status": "processing"
}
2025-07-17 03:13:45 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_medio"
}
2025-07-17 03:13:46 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 03:13:46 [INFO]: Prompt obtenido para procesamiento
Metadata: {
  "processingId": "proc_1752732825643_ajj80lm69",
  "isCustom": false,
  "medio": "test_medio"
}
2025-07-17 03:13:46 [ERROR]: Error en el sistema
Error: OpenAI service no está disponible
    at OpenAIService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\openaiService.js:77:15)
    at ProcessingService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\processingService.js:78:48)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Immediate.<anonymous> (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:84:34)
Metadata: {
  "type": "system_error",
  "error": "OpenAI service no está disponible",
  "context": {
    "context": "OpenAIService.processNews",
    "newsId": "72cf6839-98be-4f03-93d3-1e5a1b33d12e",
    "medio": "test_medio"
  }
}
2025-07-17 03:13:46 [ERROR]: Error en el sistema
Error: Error en OpenAI: OpenAI service no está disponible
    at ProcessingService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\processingService.js:85:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Immediate.<anonymous> (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:84:34)
Metadata: {
  "type": "system_error",
  "error": "Error en OpenAI: OpenAI service no está disponible",
  "context": {
    "context": "ProcessingService.processNews",
    "processingId": "proc_1752732825643_ajj80lm69",
    "newsId": "72cf6839-98be-4f03-93d3-1e5a1b33d12e",
    "medio": "test_medio"
  }
}
2025-07-17 03:13:46 [INFO]: Procesamiento registrado en métricas
Metadata: {
  "success": false,
  "tokensUsed": 0,
  "processingTime": 380,
  "totalProcessed": 1
}
2025-07-17 03:13:46 [ERROR]: Error en procesamiento automático
Metadata: {
  "diarioId": "72cf6839-98be-4f03-93d3-1e5a1b33d12e",
  "error": "Error en OpenAI: OpenAI service no está disponible"
}
2025-07-17 03:14:37 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 03:14:37 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 03:14:37 [INFO]: Servicio de monitoreo inicializado
Metadata: {
  "startTime": "2025-07-17T06:14:37.435Z"
}
2025-07-17 03:14:37 [INFO]: Cliente OpenAI inicializado correctamente
Metadata: {
  "model": "gpt-3.5-turbo",
  "maxTokens": 2000
}
2025-07-17 03:14:37 [INFO]: Cliente OpenAI inicializado correctamente
Metadata: {
  "model": "gpt-3.5-turbo",
  "maxTokens": 2000
}
2025-07-17 03:14:37 [INFO]: Servicios de procesamiento inicializados
Metadata: {
  "openai": true,
  "prompts": true,
  "database": true
}
2025-07-17 03:14:37 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 03:14:58 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 03:14:58 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:14:58 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:14:58 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:14:58 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 03:14:58 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:14:58 [INFO]: Creando diario con cliente optimizado
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:14:58 [INFO]: Datos preparados para inserción
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de proc...",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ]
  }
}
2025-07-17 03:14:58 [INFO]: Diario creado exitosamente
Metadata: {
  "id": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:14:58 [INFO]: Diario creado exitosamente con servicio híbrido
Metadata: {
  "id": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:14:58 [INFO]: Procesamiento de noticia database_save_completed
Metadata: {
  "type": "news_processing",
  "newsId": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "status": "database_save_completed"
}
2025-07-17 03:14:59 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "59ab8855-68ac-4337-8701-6bdb3e015ddc",
  "diario_id": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "status": "pending"
}
2025-07-17 03:14:59 [INFO]: Diario guardado y preparado para procesamiento
Metadata: {
  "id": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
  "content_length": 364,
  "has_images": true,
  "categories_count": 3,
  "processing_id": "59ab8855-68ac-4337-8701-6bdb3e015ddc"
}
2025-07-17 03:14:59 [INFO]: Procesamiento de noticia starting_openai_processing
Metadata: {
  "type": "news_processing",
  "newsId": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "status": "starting_openai_processing"
}
2025-07-17 03:14:59 [INFO]: Iniciando procesamiento automático en background
Metadata: {
  "diarioId": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "medio": "test_medio"
}
2025-07-17 03:14:59 [INFO]: Iniciando procesamiento de noticia
Metadata: {
  "processingId": "proc_1752732899523_wvwa66mel",
  "newsId": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "medio": "test_medio",
  "options": {
    "background": true,
    "priority": "normal"
  }
}
2025-07-17 03:14:59 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 03:14:59 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:14:59 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 03:14:59 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 03:14:59 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:14:59 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "c61e00c9-d2cd-4c97-8a22-7cd63958c7af",
  "diario_id": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "status": "processing"
}
2025-07-17 03:14:59 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_medio"
}
2025-07-17 03:14:59 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 03:14:59 [INFO]: Prompt obtenido para procesamiento
Metadata: {
  "processingId": "proc_1752732899523_wvwa66mel",
  "isCustom": false,
  "medio": "test_medio"
}
2025-07-17 03:14:59 [INFO]: Iniciando procesamiento con OpenAI
Metadata: {
  "newsId": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "medio": "test_medio",
  "titleLength": 65,
  "contentLength": 364
}
2025-07-17 03:14:59 [INFO]: Prompt construido
Metadata: {
  "promptLength": 1303,
  "newsId": "05598b45-7ee3-4b7b-aca3-e52f4309c757"
}
2025-07-17 03:15:05 [INFO]: Procesamiento OpenAI completado
Metadata: {
  "newsId": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "tokensUsed": 816,
  "processingTime": 5148,
  "outputLength": 1922,
  "model": "gpt-3.5-turbo-0125"
}
2025-07-17 03:15:05 [INFO]: Procesamiento registrado en métricas
Metadata: {
  "success": true,
  "tokensUsed": 816,
  "processingTime": 5527,
  "totalProcessed": 1
}
2025-07-17 03:15:05 [INFO]: Procesamiento completado exitosamente
Metadata: {
  "processingId": "proc_1752732899523_wvwa66mel",
  "newsId": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "tokensUsed": 816,
  "processingTime": 5527,
  "outputLength": 1922
}
2025-07-17 03:15:05 [INFO]: Procesamiento automático completado
Metadata: {
  "diarioId": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "processingId": "proc_1752732899523_wvwa66mel",
  "tokensUsed": 816,
  "processingTime": 5148
}
2025-07-17 03:15:39 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/metrics",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 03:15:49 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/health-report",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 03:16:00 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 03:16:00 [INFO]: Consultando noticias recientes
Metadata: {
  "limit": 10,
  "offset": 0,
  "requestedBy": "::1"
}
2025-07-17 03:16:46 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news/05598b45-7ee3-4b7b-aca3-e52f4309c757/process",
  "ip": "::1",
  "userAgent": "Test-Manual-Processing/1.0",
  "contentType": "application/json",
  "contentLength": "0"
}
2025-07-17 03:16:47 [INFO]: Iniciando procesamiento manual
Metadata: {
  "diarioId": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "medio": "test_medio",
  "requestedBy": "::1"
}
2025-07-17 03:16:47 [INFO]: Iniciando procesamiento de noticia
Metadata: {
  "processingId": "proc_1752733007484_6rckhru3w",
  "newsId": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "medio": "test_medio",
  "options": {
    "manual": true,
    "priority": "high"
  }
}
2025-07-17 03:16:48 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "2a81ea83-b3e7-41ee-8e3a-db057d557005",
  "diario_id": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "status": "processing"
}
2025-07-17 03:16:48 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_medio"
}
2025-07-17 03:16:48 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 03:16:48 [INFO]: Prompt obtenido para procesamiento
Metadata: {
  "processingId": "proc_1752733007484_6rckhru3w",
  "isCustom": false,
  "medio": "test_medio"
}
2025-07-17 03:16:48 [INFO]: Iniciando procesamiento con OpenAI
Metadata: {
  "newsId": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "medio": "test_medio",
  "titleLength": 65,
  "contentLength": 364
}
2025-07-17 03:16:48 [INFO]: Prompt construido
Metadata: {
  "promptLength": 1303,
  "newsId": "05598b45-7ee3-4b7b-aca3-e52f4309c757"
}
2025-07-17 03:16:56 [INFO]: Procesamiento OpenAI completado
Metadata: {
  "newsId": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "tokensUsed": 926,
  "processingTime": 8246,
  "outputLength": 2372,
  "model": "gpt-3.5-turbo-0125"
}
2025-07-17 03:16:56 [INFO]: Procesamiento registrado en métricas
Metadata: {
  "success": true,
  "tokensUsed": 926,
  "processingTime": 9079,
  "totalProcessed": 2
}
2025-07-17 03:16:56 [INFO]: Procesamiento completado exitosamente
Metadata: {
  "processingId": "proc_1752733007484_6rckhru3w",
  "newsId": "05598b45-7ee3-4b7b-aca3-e52f4309c757",
  "tokensUsed": 926,
  "processingTime": 9079,
  "outputLength": 2372
}
2025-07-17 03:16:56 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/news/05598b45-7ee3-4b7b-aca3-e52f4309c757/content",
  "ip": "::1",
  "userAgent": "Test-Manual-Processing/1.0"
}
2025-07-17 03:16:57 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/metrics",
  "ip": "::1",
  "userAgent": "Test-Manual-Processing/1.0"
}
2025-07-17 03:17:08 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/process-pending?limit=3",
  "ip": "::1",
  "userAgent": "Test-Batch-Processing/1.0",
  "contentType": "application/json",
  "contentLength": "0"
}
2025-07-17 03:17:08 [INFO]: Iniciando procesamiento en lote
Metadata: {
  "limit": 3,
  "requestedBy": "::1"
}
2025-07-17 03:17:08 [INFO]: Iniciando procesamiento de noticias pendientes
Metadata: {
  "limit": 3
}
2025-07-17 03:17:10 [INFO]: Noticias pendientes filtradas
Metadata: {
  "total": 6,
  "processed": 0,
  "pending": 3
}
2025-07-17 03:17:10 [INFO]: Noticias pendientes encontradas
Metadata: {
  "count": 3
}
2025-07-17 03:17:10 [INFO]: Iniciando procesamiento de noticia
Metadata: {
  "processingId": "proc_1752733030066_zsuy59p1w",
  "newsId": "94ed97b1-8cf6-48ba-942c-6d29eaea0465",
  "medio": "test_sql",
  "options": {}
}
2025-07-17 03:17:10 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "a9928cea-357a-4b11-92d7-39b561ad4777",
  "diario_id": "94ed97b1-8cf6-48ba-942c-6d29eaea0465",
  "status": "processing"
}
2025-07-17 03:17:10 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_sql"
}
2025-07-17 03:17:10 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_sql"
  }
}
2025-07-17 03:17:10 [INFO]: Prompt obtenido para procesamiento
Metadata: {
  "processingId": "proc_1752733030066_zsuy59p1w",
  "isCustom": false,
  "medio": "test_sql"
}
2025-07-17 03:17:10 [INFO]: Iniciando procesamiento con OpenAI
Metadata: {
  "newsId": "94ed97b1-8cf6-48ba-942c-6d29eaea0465",
  "medio": "test_sql",
  "titleLength": 10,
  "contentLength": 65
}
2025-07-17 03:17:10 [INFO]: Prompt construido
Metadata: {
  "promptLength": 821,
  "newsId": "94ed97b1-8cf6-48ba-942c-6d29eaea0465"
}
2025-07-17 03:17:13 [INFO]: Procesamiento OpenAI completado
Metadata: {
  "newsId": "94ed97b1-8cf6-48ba-942c-6d29eaea0465",
  "tokensUsed": 488,
  "processingTime": 3171,
  "outputLength": 859,
  "model": "gpt-3.5-turbo-0125"
}
2025-07-17 03:17:13 [INFO]: Procesamiento registrado en métricas
Metadata: {
  "success": true,
  "tokensUsed": 488,
  "processingTime": 3552,
  "totalProcessed": 3
}
2025-07-17 03:17:13 [INFO]: Procesamiento completado exitosamente
Metadata: {
  "processingId": "proc_1752733030066_zsuy59p1w",
  "newsId": "94ed97b1-8cf6-48ba-942c-6d29eaea0465",
  "tokensUsed": 488,
  "processingTime": 3552,
  "outputLength": 859
}
2025-07-17 03:17:13 [INFO]: Iniciando procesamiento de noticia
Metadata: {
  "processingId": "proc_1752733033618_x6uve4g2g",
  "newsId": "703415e7-7184-479a-82ef-12cae1a6fa26",
  "medio": "test_selfhosted",
  "options": {}
}
2025-07-17 03:17:13 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "93718270-3ae6-4473-ad2b-b62eda2f14ca",
  "diario_id": "703415e7-7184-479a-82ef-12cae1a6fa26",
  "status": "processing"
}
2025-07-17 03:17:13 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_selfhosted"
}
2025-07-17 03:17:13 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_selfhosted"
  }
}
2025-07-17 03:17:13 [INFO]: Prompt obtenido para procesamiento
Metadata: {
  "processingId": "proc_1752733033618_x6uve4g2g",
  "isCustom": false,
  "medio": "test_selfhosted"
}
2025-07-17 03:17:14 [INFO]: Iniciando procesamiento con OpenAI
Metadata: {
  "newsId": "703415e7-7184-479a-82ef-12cae1a6fa26",
  "medio": "test_selfhosted",
  "titleLength": 34,
  "contentLength": 100
}
2025-07-17 03:17:14 [INFO]: Prompt construido
Metadata: {
  "promptLength": 887,
  "newsId": "703415e7-7184-479a-82ef-12cae1a6fa26"
}
2025-07-17 03:17:18 [INFO]: Procesamiento OpenAI completado
Metadata: {
  "newsId": "703415e7-7184-479a-82ef-12cae1a6fa26",
  "tokensUsed": 784,
  "processingTime": 4354,
  "outputLength": 1985,
  "model": "gpt-3.5-turbo-0125"
}
2025-07-17 03:17:18 [INFO]: Procesamiento registrado en métricas
Metadata: {
  "success": true,
  "tokensUsed": 784,
  "processingTime": 4736,
  "totalProcessed": 4
}
2025-07-17 03:17:18 [INFO]: Procesamiento completado exitosamente
Metadata: {
  "processingId": "proc_1752733033618_x6uve4g2g",
  "newsId": "703415e7-7184-479a-82ef-12cae1a6fa26",
  "tokensUsed": 784,
  "processingTime": 4736,
  "outputLength": 1985
}
2025-07-17 03:17:18 [INFO]: Iniciando procesamiento de noticia
Metadata: {
  "processingId": "proc_1752733038354_vx1uzm54a",
  "newsId": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "medio": "test_medio",
  "options": {}
}
2025-07-17 03:17:18 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "cf1375b6-f840-4ca4-bf37-ac8da02471c0",
  "diario_id": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "status": "processing"
}
2025-07-17 03:17:18 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_medio"
}
2025-07-17 03:17:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 03:17:19 [INFO]: Prompt obtenido para procesamiento
Metadata: {
  "processingId": "proc_1752733038354_vx1uzm54a",
  "isCustom": false,
  "medio": "test_medio"
}
2025-07-17 03:17:19 [INFO]: Iniciando procesamiento con OpenAI
Metadata: {
  "newsId": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "medio": "test_medio",
  "titleLength": 65,
  "contentLength": 364
}
2025-07-17 03:17:19 [INFO]: Prompt construido
Metadata: {
  "promptLength": 1303,
  "newsId": "566a04c9-137a-4afc-af21-b4abbc260c8e"
}
2025-07-17 03:17:24 [INFO]: Procesamiento OpenAI completado
Metadata: {
  "newsId": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "tokensUsed": 948,
  "processingTime": 5034,
  "outputLength": 2500,
  "model": "gpt-3.5-turbo-0125"
}
2025-07-17 03:17:24 [INFO]: Procesamiento registrado en métricas
Metadata: {
  "success": true,
  "tokensUsed": 948,
  "processingTime": 6190,
  "totalProcessed": 5
}
2025-07-17 03:17:24 [INFO]: Procesamiento completado exitosamente
Metadata: {
  "processingId": "proc_1752733038354_vx1uzm54a",
  "newsId": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "tokensUsed": 948,
  "processingTime": 6190,
  "outputLength": 2500
}
2025-07-17 03:17:24 [INFO]: Procesamiento en lote completado
Metadata: {
  "total": 3,
  "success": 3,
  "errors": 0
}
2025-07-17 03:17:35 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 03:17:35 [INFO]: Conexión con Supabase verificada correctamente
2025-07-17 03:17:38 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 5,
  "tables": [
    "diarios",
    "medios",
    "prompts_medios",
    "system_logs",
    "news_processing"
  ]
}
2025-07-17 03:19:37 [INFO]: Métricas del sistema
Metadata: {
  "uptime": "5m 0s",
  "memory": {
    "used": "21.67 MB",
    "total": "23.52 MB",
    "external": "3.6 MB",
    "rss": "72.35 MB"
  },
  "webhooks": {
    "total": 1,
    "success": 1,
    "errors": 0,
    "lastHour": 1
  },
  "processing": {
    "total": 5,
    "success": 5,
    "errors": 0,
    "totalTokens": 3962,
    "totalCost": 0.007924,
    "averageTime": 5816.8
  }
}
2025-07-17 03:21:34 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-07-17 03:21:42 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-07-17 03:21:42 [INFO]: Consultando noticias recientes
Metadata: {
  "limit": 10,
  "offset": 0,
  "requestedBy": "::1"
}
2025-07-17 03:24:08 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Noticia-Real/1.0",
  "contentType": "application/json",
  "contentLength": "2255"
}
2025-07-17 03:24:08 [WARN]: Firma de webhook inválida
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Noticia-Real/1.0",
  "receivedSignature": "cfbf7525..."
}
2025-07-17 03:24:37 [INFO]: Métricas del sistema
Metadata: {
  "uptime": "10m 0s",
  "memory": {
    "used": "21.58 MB",
    "total": "23.77 MB",
    "external": "3.62 MB",
    "rss": "72.52 MB"
  },
  "webhooks": {
    "total": 1,
    "success": 1,
    "errors": 0,
    "lastHour": 1
  },
  "processing": {
    "total": 5,
    "success": 5,
    "errors": 0,
    "totalTokens": 3962,
    "totalCost": 0.007924,
    "averageTime": 5816.8
  }
}
2025-07-17 03:25:45 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Noticia-Real/1.0",
  "contentType": "application/json",
  "contentLength": "2255"
}
2025-07-17 03:25:45 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Noticia-Real/1.0"
}
2025-07-17 03:25:45 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "telesol_diario",
  "title": "Convocatoria en la Plaza 25: La Municipalidad de la Ciudad de San Juan emitió un comunicado"
}
2025-07-17 03:25:45 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "telesol_diario",
  "title": "Convocatoria en la Plaza 25: La Municipalidad de la Ciudad de San Juan emitió un comunicado"
}
2025-07-17 03:25:45 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 03:25:45 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "telesol_diario",
  "title": "Convocatoria en la Plaza 25: La Municipalidad de la Ciudad de San Juan emitió un comunicado"
}
2025-07-17 03:25:45 [INFO]: Creando diario con cliente optimizado
Metadata: {
  "medio_origen": "telesol_diario",
  "title": "Convocatoria en la Plaza 25: La Municipalidad de la Ciudad de San Juan emitió un comunicado"
}
2025-07-17 03:25:45 [INFO]: Datos preparados para inserción
Metadata: {
  "insertData": {
    "medio_origen": "telesol_diario",
    "header": "Evento suspendido",
    "title": "Convocatoria en la Plaza 25: La Municipalidad de la Ciudad de San Juan emitió un comunicado",
    "summary": "El evento de 'Peter Regalos' fue suspendido por la Municipalidad tras una convocatoria inesperadamente grande. Se recordó que no se permiten actos masivos en la plaza.",
    "content": "El influencer Pedro Casarino, conocido como \"Peter Regalos\", reconoció que esperaba solo 50 personas...",
    "images_url": [
      "https://www.telesoldiario.com/content/bucket/1/463431w850h479c.jpg.webp"
    ],
    "categories": [
      "política",
      "san juan",
      "municipalidad",
      "eventos públicos",
      "patrimonio histórico"
    ]
  }
}
2025-07-17 03:25:46 [INFO]: Diario creado exitosamente
Metadata: {
  "id": "37f18a75-712c-4ec6-b677-c1b491848331",
  "medio_origen": "telesol_diario",
  "title": "Convocatoria en la Plaza 25: La Municipalidad de la Ciudad de San Juan emitió un comunicado"
}
2025-07-17 03:25:46 [INFO]: Diario creado exitosamente con servicio híbrido
Metadata: {
  "id": "37f18a75-712c-4ec6-b677-c1b491848331",
  "medio_origen": "telesol_diario",
  "title": "Convocatoria en la Plaza 25: La Municipalidad de la Ciudad de San Juan emitió un comunicado"
}
2025-07-17 03:25:46 [INFO]: Procesamiento de noticia database_save_completed
Metadata: {
  "type": "news_processing",
  "newsId": "37f18a75-712c-4ec6-b677-c1b491848331",
  "status": "database_save_completed"
}
2025-07-17 03:25:47 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "0656d770-a3f6-4d8a-8997-043720457773",
  "diario_id": "37f18a75-712c-4ec6-b677-c1b491848331",
  "status": "pending"
}
2025-07-17 03:25:47 [INFO]: Diario guardado y preparado para procesamiento
Metadata: {
  "id": "37f18a75-712c-4ec6-b677-c1b491848331",
  "medio_origen": "telesol_diario",
  "title": "Convocatoria en la Plaza 25: La Municipalidad de la Ciudad de San Juan emitió un comunicado",
  "content_length": 1471,
  "has_images": true,
  "categories_count": 5,
  "processing_id": "0656d770-a3f6-4d8a-8997-043720457773"
}
2025-07-17 03:25:47 [INFO]: Procesamiento de noticia starting_openai_processing
Metadata: {
  "type": "news_processing",
  "newsId": "37f18a75-712c-4ec6-b677-c1b491848331",
  "status": "starting_openai_processing"
}
2025-07-17 03:25:47 [INFO]: Iniciando procesamiento automático en background
Metadata: {
  "diarioId": "37f18a75-712c-4ec6-b677-c1b491848331",
  "medio": "telesol_diario"
}
2025-07-17 03:25:47 [INFO]: Iniciando procesamiento de noticia
Metadata: {
  "processingId": "proc_1752733547288_ppp022w31",
  "newsId": "37f18a75-712c-4ec6-b677-c1b491848331",
  "medio": "telesol_diario",
  "options": {
    "background": true,
    "priority": "normal"
  }
}
2025-07-17 03:25:47 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "2f8704a7-491a-4d95-ab1e-abcea61bdb78",
  "diario_id": "37f18a75-712c-4ec6-b677-c1b491848331",
  "status": "processing"
}
2025-07-17 03:25:47 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "telesol_diario"
}
2025-07-17 03:25:47 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "telesol_diario"
  }
}
2025-07-17 03:25:47 [INFO]: Prompt obtenido para procesamiento
Metadata: {
  "processingId": "proc_1752733547288_ppp022w31",
  "isCustom": false,
  "medio": "telesol_diario"
}
2025-07-17 03:25:47 [INFO]: Iniciando procesamiento con OpenAI
Metadata: {
  "newsId": "37f18a75-712c-4ec6-b677-c1b491848331",
  "medio": "telesol_diario",
  "titleLength": 91,
  "contentLength": 1471
}
2025-07-17 03:25:47 [INFO]: Prompt construido
Metadata: {
  "promptLength": 2481,
  "newsId": "37f18a75-712c-4ec6-b677-c1b491848331"
}
2025-07-17 03:25:56 [INFO]: Procesamiento OpenAI completado
Metadata: {
  "newsId": "37f18a75-712c-4ec6-b677-c1b491848331",
  "tokensUsed": 1131,
  "processingTime": 8325,
  "outputLength": 1755,
  "model": "gpt-3.5-turbo-0125"
}
2025-07-17 03:25:56 [INFO]: Procesamiento registrado en métricas
Metadata: {
  "success": true,
  "tokensUsed": 1131,
  "processingTime": 8742,
  "totalProcessed": 6
}
2025-07-17 03:25:56 [INFO]: Procesamiento completado exitosamente
Metadata: {
  "processingId": "proc_1752733547288_ppp022w31",
  "newsId": "37f18a75-712c-4ec6-b677-c1b491848331",
  "tokensUsed": 1131,
  "processingTime": 8742,
  "outputLength": 1755
}
2025-07-17 03:25:56 [INFO]: Procesamiento automático completado
Metadata: {
  "diarioId": "37f18a75-712c-4ec6-b677-c1b491848331",
  "processingId": "proc_1752733547288_ppp022w31",
  "tokensUsed": 1131,
  "processingTime": 8325
}
2025-07-17 03:26:02 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/news/37f18a75-712c-4ec6-b677-c1b491848331/status",
  "ip": "::1",
  "userAgent": "Test-Noticia-Real/1.0"
}
2025-07-17 03:26:03 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/metrics",
  "ip": "::1",
  "userAgent": "Test-Noticia-Real/1.0"
}
2025-07-17 03:26:16 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/news/37f18a75-712c-4ec6-b677-c1b491848331/status",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 03:26:40 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/news/37f18a75-712c-4ec6-b677-c1b491848331/status",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 03:27:40 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/metrics",
  "ip": "::1",
  "userAgent": "Demo-Final/1.0"
}
2025-07-17 03:27:41 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/news?limit=5",
  "ip": "::1",
  "userAgent": "Demo-Final/1.0"
}
2025-07-17 03:27:41 [INFO]: Consultando noticias recientes
Metadata: {
  "limit": 5,
  "offset": 0,
  "requestedBy": "::1"
}
2025-07-17 03:27:41 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/news/37f18a75-712c-4ec6-b677-c1b491848331/content",
  "ip": "::1",
  "userAgent": "Demo-Final/1.0"
}
2025-07-17 03:27:42 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/health-report",
  "ip": "::1",
  "userAgent": "Demo-Final/1.0"
}
2025-07-17 03:28:10 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-07-17 03:28:10 [INFO]: Consultando noticias recientes
Metadata: {
  "limit": 10,
  "offset": 0,
  "requestedBy": "::1"
}
2025-07-17 03:29:37 [INFO]: Métricas del sistema
Metadata: {
  "uptime": "15m 0s",
  "memory": {
    "used": "22.57 MB",
    "total": "25.77 MB",
    "external": "3.71 MB",
    "rss": "73.96 MB"
  },
  "webhooks": {
    "total": 2,
    "success": 2,
    "errors": 0,
    "lastHour": 2
  },
  "processing": {
    "total": 6,
    "success": 6,
    "errors": 0,
    "totalTokens": 5093,
    "totalCost": 0.010186,
    "averageTime": 6304.333333333333
  }
}
2025-07-17 03:34:37 [INFO]: Métricas del sistema
Metadata: {
  "uptime": "20m 0s",
  "memory": {
    "used": "22.6 MB",
    "total": "25.77 MB",
    "external": "3.71 MB",
    "rss": "73.96 MB"
  },
  "webhooks": {
    "total": 2,
    "success": 2,
    "errors": 0,
    "lastHour": 2
  },
  "processing": {
    "total": 6,
    "success": 6,
    "errors": 0,
    "totalTokens": 5093,
    "totalCost": 0.010186,
    "averageTime": 6304.333333333333
  }
}
2025-07-17 03:35:29 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
2025-07-17 03:35:29 [INFO]: Consultando noticias recientes
Metadata: {
  "limit": 10,
  "offset": 0,
  "requestedBy": "::1"
}
