2025-07-17 01:50:51 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 01:51:15 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:51:15 [WARN]: Ruta no encontrada
Metadata: {
  "method": "GET",
  "url": "/",
  "ip": "::1"
}
2025-07-17 01:51:44 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 01:51:58 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:51:58 [WARN]: Ruta no encontrada
Metadata: {
  "method": "GET",
  "url": "/",
  "ip": "::1"
}
2025-07-17 01:52:36 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 01:52:50 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:52:56 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 01:53:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 01:53:35 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 01:53:35 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 01:53:35 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 01:53:35 [INFO]: Noticia recibida y preparada para procesamiento
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
  "content_length": 364,
  "has_images": true,
  "categories_count": 3
}
2025-07-17 01:53:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 01:53:35 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 01:53:35 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 01:53:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 01:53:35 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:00:34 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:00:34 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:02:04 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:02:04 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:02:04 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:02:20 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:02:20 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "TypeError: fetch failed",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:02:20 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 0,
  "tables": []
}
2025-07-17 02:02:27 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/ready",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:02:27 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "TypeError: fetch failed",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:02:27 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 0,
  "tables": []
}
2025-07-17 02:04:01 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:04:01 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:04:01 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:04:17 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:04:17 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:05:16 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:05:16 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:05:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.information_schema.tables\" does not exist",
  "context": {
    "context": "testSupabaseConnection"
  }
}
2025-07-17 02:06:14 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:06:14 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:06:14 [INFO]: Conexión con Supabase verificada correctamente
2025-07-17 02:06:17 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 5,
  "tables": [
    "noticias",
    "medios",
    "prompts_medios",
    "system_logs",
    "news_processing"
  ]
}
2025-07-17 02:06:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.medios\" does not exist",
  "context": {
    "context": "getMediosActivos",
    "code": "42P01",
    "message": "relation \"public.medios\" does not exist",
    "details": null,
    "hint": null
  }
}
2025-07-17 02:06:18 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:06:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:06:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createMedio"
  }
}
2025-07-17 02:07:18 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:07:19 [INFO]: Conexión con Supabase verificada correctamente
2025-07-17 02:07:22 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 5,
  "tables": [
    "noticias",
    "medios",
    "prompts_medios",
    "system_logs",
    "news_processing"
  ]
}
2025-07-17 02:08:05 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:08:05 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:08:15 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:08:15 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:08:15 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:08:15 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:08:15 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:08:16 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createNoticia"
  }
}
2025-07-17 02:08:16 [ERROR]: Error en el sistema
Error: Error guardando noticia en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando noticia en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:08:16 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:08:16 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:08:16 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:08:16 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:08:16 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:08:57 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:08:57 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:08:57 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:24:19 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:24:19 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:24:19 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:24:38 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:24:38 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:24:48 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:24:49 [INFO]: Conexión con Supabase verificada correctamente
2025-07-17 02:24:52 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 5,
  "tables": [
    "diarios",
    "medios",
    "prompts_medios",
    "system_logs",
    "news_processing"
  ]
}
2025-07-17 02:25:00 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:25:00 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:25:00 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:25:00 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:25:00 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:25:00 [INFO]: Intentando crear diario en Supabase
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:25:00 [INFO]: Datos a insertar
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de procesamiento automatizado que utiliza inteligencia artificial para reescribir noticias según diferentes formatos de medios de comunicación. Este sistema integra OpenAI API con Supabase para almacenar tanto las versiones originales como las reescritas de las noticias.",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ],
    "created_at": "2025-07-17T05:25:00.334Z"
  }
}
2025-07-17 02:25:00 [ERROR]: Error de Supabase al insertar diario
Metadata: {}
2025-07-17 02:25:00 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:25:00 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:25:00 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:25:00 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:25:00 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:25:00 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:25:00 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:25:00 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:30:05 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/ready",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:30:06 [INFO]: Conexión con Supabase verificada correctamente
2025-07-17 02:30:09 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 5,
  "tables": [
    "diarios",
    "medios",
    "prompts_medios",
    "system_logs",
    "news_processing"
  ]
}
2025-07-17 02:30:19 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:30:19 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:30:19 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:30:19 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:30:19 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:30:19 [INFO]: Intentando crear diario en Supabase
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:30:19 [INFO]: Datos a insertar
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de procesamiento automatizado que utiliza inteligencia artificial para reescribir noticias según diferentes formatos de medios de comunicación. Este sistema integra OpenAI API con Supabase para almacenar tanto las versiones originales como las reescritas de las noticias.",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ],
    "created_at": "2025-07-17T05:30:19.170Z"
  }
}
2025-07-17 02:30:19 [ERROR]: Error de Supabase al insertar diario
Metadata: {}
2025-07-17 02:30:19 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:30:19 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:30:19 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:30:19 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:30:19 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:30:19 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:30:19 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:30:19 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:30:58 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:30:58 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:30:58 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:31:22 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:31:22 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:31:22 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:31:22 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:31:22 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:31:22 [INFO]: Intentando crear diario en Supabase
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:31:22 [INFO]: Datos a insertar
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de procesamiento automatizado que utiliza inteligencia artificial para reescribir noticias según diferentes formatos de medios de comunicación. Este sistema integra OpenAI API con Supabase para almacenar tanto las versiones originales como las reescritas de las noticias.",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ],
    "created_at": "2025-07-17T05:31:22.082Z"
  }
}
2025-07-17 02:31:22 [ERROR]: Error de Supabase al insertar diario
Metadata: {
  "fullError": "{}"
}
2025-07-17 02:31:22 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:31:22 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:31:22 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:31:22 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:31:22 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:31:22 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:31:22 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:31:22 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:32:10 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:32:10 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:35:30 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:35:30 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:35:30 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:35:30 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:35:30 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:35:30 [INFO]: Intentando crear diario en Supabase
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:35:30 [INFO]: Datos a insertar
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de procesamiento automatizado que utiliza inteligencia artificial para reescribir noticias según diferentes formatos de medios de comunicación. Este sistema integra OpenAI API con Supabase para almacenar tanto las versiones originales como las reescritas de las noticias.",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ],
    "created_at": "2025-07-17T05:35:30.570Z"
  }
}
2025-07-17 02:35:31 [ERROR]: Error de Supabase al insertar diario
Metadata: {
  "fullError": "{}"
}
2025-07-17 02:35:31 [ERROR]: Error en createDiario
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:35:31 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "createDiario"
  }
}
2025-07-17 02:35:31 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:35:31 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:35:31 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:35:31 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:35:31 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:35:31 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:35:53 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:35:53 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:36:36 [INFO]: SIGINT recibido, cerrando servidor...
2025-07-17 02:41:13 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:41:13 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:41:13 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:41:30 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:41:30 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:43:19 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:43:19 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:43:19 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:43:35 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:43:35 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:43:35 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:43:35 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:43:35 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:43:35 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:43:35 [INFO]: Creando diario con SQL directo
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:43:35 [INFO]: Ejecutando SQL de inserción
Metadata: {
  "sql": "\n        INSERT INTO diarios (\n          medio_origen, \n          header, \n          title, \n          summary, \n          content, \n          images_url, \n          categories,\n          created_at,\n...",
  "dataLength": 829
}
2025-07-17 02:43:36 [ERROR]: Error en SQL directo
Metadata: {
  "error": "Could not find the function public.exec_sql(sql_query) in the schema cache",
  "code": "PGRST202",
  "details": "Searched for the function public.exec_sql with parameter sql_query or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.",
  "hint": null
}
2025-07-17 02:43:36 [ERROR]: Error en createDiario híbrido
Metadata: {
  "error": "Could not find the function public.exec_sql(sql_query) in the schema cache",
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:43:36 [ERROR]: Error en servicio híbrido
Metadata: {
  "error": {
    "code": "HYBRID_SQL_ERROR",
    "message": "Could not find the function public.exec_sql(sql_query) in the schema cache",
    "originalError": {
      "code": "PGRST202",
      "details": "Searched for the function public.exec_sql with parameter sql_query or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.",
      "hint": null,
      "message": "Could not find the function public.exec_sql(sql_query) in the schema cache"
    }
  },
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:43:36 [ERROR]: Error en createDiario
Error: Could not find the function public.exec_sql(sql_query) in the schema cache
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "error": "Could not find the function public.exec_sql(sql_query) in the schema cache",
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:43:36 [ERROR]: Error en el sistema
Error: Could not find the function public.exec_sql(sql_query) in the schema cache
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "type": "system_error",
  "error": "Could not find the function public.exec_sql(sql_query) in the schema cache",
  "context": {
    "context": "createDiario",
    "message": "Could not find the function public.exec_sql(sql_query) in the schema cache"
  }
}
2025-07-17 02:43:36 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "message": "Could not find the function public.exec_sql(sql_query) in the schema cache",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:43:36 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:43:36 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:43:36 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:43:36 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:43:36 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:44:46 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:44:46 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:44:46 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:45:06 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:45:06 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:45:06 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:45:06 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:45:06 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:45:06 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:45:06 [INFO]: Creando diario con cliente optimizado
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:45:06 [INFO]: Datos preparados para inserción
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de proc...",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ]
  }
}
2025-07-17 02:45:07 [WARN]: Inserción estándar falló, intentando alternativa
Metadata: {}
2025-07-17 02:45:07 [INFO]: Intentando inserción sin select
2025-07-17 02:45:07 [ERROR]: Error en createDiario híbrido
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:45:07 [ERROR]: Error en servicio híbrido
Metadata: {
  "error": {
    "code": "HYBRID_INSERT_ERROR",
    "originalError": {}
  },
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:45:07 [ERROR]: Error en createDiario
Error
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "error": "",
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:45:07 [ERROR]: Error en el sistema
Error
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "type": "system_error",
  "error": "",
  "context": {
    "context": "createDiario",
    "message": ""
  }
}
2025-07-17 02:45:07 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "message": "",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:45:07 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:45:07 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:45:07 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:45:07 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:45:07 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:45:58 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:45:58 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:45:58 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:46:19 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:46:19 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:46:19 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:46:19 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:46:19 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:46:19 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:46:19 [INFO]: Creando diario con cliente optimizado
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:46:19 [INFO]: Datos preparados para inserción
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de proc...",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ]
  }
}
2025-07-17 02:46:20 [WARN]: Inserción estándar falló, intentando alternativa
Metadata: {
  "fullError": "{}"
}
2025-07-17 02:46:20 [INFO]: Intentando inserción sin select
2025-07-17 02:46:20 [ERROR]: Inserción sin select también falló
Metadata: {
  "fullError": "{}"
}
2025-07-17 02:46:20 [ERROR]: Error en createDiario híbrido
Metadata: {
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:46:20 [ERROR]: Error en servicio híbrido
Metadata: {
  "error": {
    "code": "HYBRID_INSERT_ERROR",
    "originalError": {}
  },
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:46:20 [ERROR]: Error en createDiario
Error
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "error": "",
  "diarioData": {
    "medio_origen": "test_medio",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
  }
}
2025-07-17 02:46:20 [ERROR]: Error en el sistema
Error
    at DatabaseService.createDiario (G:\DEL SUR FINAL\demo ia server\src\services\databaseService.js:42:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:17:26)
Metadata: {
  "type": "system_error",
  "error": "",
  "context": {
    "context": "createDiario",
    "message": ""
  }
}
2025-07-17 02:46:20 [ERROR]: Error en el sistema
Error: Error guardando diario en BD
    at receiveNewsWebhook (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:20:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
Metadata: {
  "type": "system_error",
  "error": "Error guardando diario en BD",
  "context": {
    "context": "receiveNewsWebhook",
    "error": {
      "code": "DATABASE_ERROR",
      "message": "",
      "originalError": {}
    },
    "newsData": {
      "medio_origen": "test_medio",
      "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
    }
  }
}
2025-07-17 02:46:20 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:46:20 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:46:20 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:46:20 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:46:20 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:50:02 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:50:02 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:50:02 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 02:50:36 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:50:36 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:50:36 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:50:36 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:50:36 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 02:50:36 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:50:36 [INFO]: Creando diario con cliente optimizado
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:50:36 [INFO]: Datos preparados para inserción
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de proc...",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ]
  }
}
2025-07-17 02:50:36 [INFO]: Diario creado exitosamente
Metadata: {
  "id": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:50:36 [INFO]: Diario creado exitosamente con servicio híbrido
Metadata: {
  "id": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 02:50:36 [INFO]: Procesamiento de noticia database_save_completed
Metadata: {
  "type": "news_processing",
  "newsId": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "status": "database_save_completed"
}
2025-07-17 02:50:37 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "b3a4ef5c-9c9f-4ef6-917d-64617c3d28bf",
  "diario_id": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "status": "pending"
}
2025-07-17 02:50:37 [INFO]: Diario guardado y preparado para procesamiento
Metadata: {
  "id": "566a04c9-137a-4afc-af21-b4abbc260c8e",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
  "content_length": 364,
  "has_images": true,
  "categories_count": 3,
  "processing_id": "b3a4ef5c-9c9f-4ef6-917d-64617c3d28bf"
}
2025-07-17 02:50:37 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 02:50:37 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:50:37 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 02:50:37 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 02:50:37 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 02:50:48 [INFO]: Request recibido
Metadata: {
  "method": "GET",
  "url": "/health",
  "ip": "::1",
  "userAgent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; es-AR) WindowsPowerShell/5.1.22621.5624"
}
2025-07-17 02:50:49 [INFO]: Conexión con Supabase verificada correctamente
2025-07-17 02:50:52 [INFO]: Verificación de tablas completada
Metadata: {
  "totalTables": 5,
  "existingTables": 5,
  "tables": [
    "diarios",
    "medios",
    "prompts_medios",
    "system_logs",
    "news_processing"
  ]
}
2025-07-17 02:54:01 [WARN]: OpenAI API key no configurada o es temporal
Metadata: {
  "hasKey": true,
  "isTemp": true
}
2025-07-17 02:55:44 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:55:44 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:55:44 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_medio"
}
2025-07-17 02:55:44 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 02:55:44 [INFO]: Guardando prompt personalizado
Metadata: {
  "medio": "test_medio_custom",
  "hasTemplate": true
}
2025-07-17 02:55:45 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "context": {
    "context": "PromptService.savePromptForMedio",
    "medio": "test_medio_custom"
  }
}
2025-07-17 02:55:45 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_medio_custom"
}
2025-07-17 02:55:45 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio_custom"
  }
}
2025-07-17 02:55:46 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getAllActivePrompts"
  }
}
2025-07-17 02:55:46 [INFO]: Cache de prompts limpiado
2025-07-17 02:55:46 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_medio"
}
2025-07-17 02:55:46 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 02:55:57 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:55:57 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:58:02 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 02:58:02 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 02:58:02 [INFO]: Cliente OpenAI inicializado correctamente
Metadata: {
  "model": "gpt-3.5-turbo",
  "maxTokens": 2000
}
2025-07-17 02:58:02 [INFO]: Servicios de procesamiento inicializados
Metadata: {
  "openai": true,
  "prompts": true,
  "database": true
}
2025-07-17 02:58:05 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "\"failed to parse logic tree ((news_processing.status.is.null,news_processing.status.eq.pending))\" (line 1, column 20)",
  "context": {
    "context": "ProcessingService.getPendingNews"
  }
}
2025-07-17 02:58:12 [INFO]: Estadísticas de procesamiento reseteadas
2025-07-17 03:01:30 [INFO]: Cliente Supabase Service inicializado correctamente
2025-07-17 03:01:30 [INFO]: Cliente Supabase Anon inicializado correctamente
2025-07-17 03:01:30 [INFO]: Servidor iniciado correctamente
Metadata: {
  "port": "3000",
  "environment": "development"
}
2025-07-17 03:01:50 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 03:01:50 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:01:50 [INFO]: Validación de webhook exitosa
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:01:50 [INFO]: Webhook recibido
Metadata: {
  "type": "webhook",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:01:50 [INFO]: Procesamiento de noticia starting_database_save
Metadata: {
  "type": "news_processing",
  "newsId": "unknown",
  "status": "starting_database_save"
}
2025-07-17 03:01:50 [INFO]: Creando diario usando servicio híbrido
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:01:50 [INFO]: Creando diario con cliente optimizado
Metadata: {
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:01:50 [INFO]: Datos preparados para inserción
Metadata: {
  "insertData": {
    "medio_origen": "test_medio",
    "header": "Titular de prueba para testing del webhook",
    "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
    "summary": "Esta es una noticia de prueba para validar el funcionamiento del sistema de procesamiento automatizado de noticias con OpenAI.",
    "content": "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de proc...",
    "images_url": [
      "https://ejemplo.com/imagen1.jpg",
      "https://ejemplo.com/imagen2.jpg"
    ],
    "categories": [
      "tecnología",
      "inteligencia artificial",
      "automatización"
    ]
  }
}
2025-07-17 03:01:51 [INFO]: Diario creado exitosamente
Metadata: {
  "id": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:01:51 [INFO]: Diario creado exitosamente con servicio híbrido
Metadata: {
  "id": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente"
}
2025-07-17 03:01:51 [INFO]: Procesamiento de noticia database_save_completed
Metadata: {
  "type": "news_processing",
  "newsId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "status": "database_save_completed"
}
2025-07-17 03:01:51 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "b93218aa-3836-4200-a000-2aea5be43948",
  "diario_id": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "status": "pending"
}
2025-07-17 03:01:51 [INFO]: Diario guardado y preparado para procesamiento
Metadata: {
  "id": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "medio_origen": "test_medio",
  "title": "Noticia de prueba: Sistema de IA procesa noticias automáticamente",
  "content_length": 364,
  "has_images": true,
  "categories_count": 3,
  "processing_id": "b93218aa-3836-4200-a000-2aea5be43948"
}
2025-07-17 03:01:51 [INFO]: Procesamiento de noticia starting_openai_processing
Metadata: {
  "type": "news_processing",
  "newsId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "status": "starting_openai_processing"
}
2025-07-17 03:01:51 [INFO]: Iniciando procesamiento automático en background
Metadata: {
  "diarioId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "medio": "test_medio"
}
2025-07-17 03:01:51 [INFO]: Iniciando procesamiento de noticia
Metadata: {
  "processingId": "proc_1752732111989_t16g16n1p",
  "newsId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "medio": "test_medio",
  "options": {
    "background": true,
    "priority": "normal"
  }
}
2025-07-17 03:01:51 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "83"
}
2025-07-17 03:01:51 [INFO]: Webhook autenticado correctamente
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:01:51 [WARN]: Validación de webhook fallida
Metadata: {
  "errors": [
    {
      "field": "header",
      "message": "header es un campo obligatorio"
    },
    {
      "field": "content",
      "message": "content debe tener al menos 50 caracteres",
      "value": "Contenido muy corto"
    }
  ],
  "payload": {
    "medio_origen": "test",
    "title": "Título muy corto",
    "content": "Contenido muy corto"
  }
}
2025-07-17 03:01:51 [INFO]: Request recibido
Metadata: {
  "method": "POST",
  "url": "/webhook/news",
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0",
  "contentType": "application/json",
  "contentLength": "835"
}
2025-07-17 03:01:51 [WARN]: Webhook sin firma recibido
Metadata: {
  "ip": "::1",
  "userAgent": "Test-Webhook-Client/1.0"
}
2025-07-17 03:01:52 [INFO]: Registro de procesamiento creado
Metadata: {
  "id": "e474002f-5901-4f4e-a1fc-7693d4aa4110",
  "diario_id": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "status": "processing"
}
2025-07-17 03:01:52 [INFO]: Obteniendo prompt desde base de datos
Metadata: {
  "medio": "test_medio"
}
2025-07-17 03:01:52 [ERROR]: Error en el sistema
Metadata: {
  "type": "system_error",
  "error": "relation \"public.prompts_medios\" does not exist",
  "context": {
    "context": "PromptService.getPromptForMedio",
    "medio": "test_medio"
  }
}
2025-07-17 03:01:52 [INFO]: Prompt obtenido para procesamiento
Metadata: {
  "processingId": "proc_1752732111989_t16g16n1p",
  "isCustom": false,
  "medio": "test_medio"
}
2025-07-17 03:01:52 [ERROR]: Error en el sistema
Error: OpenAI service no está disponible
    at OpenAIService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\openaiService.js:77:15)
    at ProcessingService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\processingService.js:77:48)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Immediate.<anonymous> (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:83:34)
Metadata: {
  "type": "system_error",
  "error": "OpenAI service no está disponible",
  "context": {
    "context": "OpenAIService.processNews",
    "newsId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
    "medio": "test_medio"
  }
}
2025-07-17 03:01:52 [ERROR]: Error en el sistema
Error: Error en OpenAI: OpenAI service no está disponible
    at ProcessingService.processNews (G:\DEL SUR FINAL\demo ia server\src\services\processingService.js:84:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Immediate.<anonymous> (G:\DEL SUR FINAL\demo ia server\src\controllers\webhookController.js:83:34)
Metadata: {
  "type": "system_error",
  "error": "Error en OpenAI: OpenAI service no está disponible",
  "context": {
    "context": "ProcessingService.processNews",
    "processingId": "proc_1752732111989_t16g16n1p",
    "newsId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
    "medio": "test_medio"
  }
}
2025-07-17 03:01:52 [ERROR]: Error en procesamiento automático
Metadata: {
  "diarioId": "a571779b-b97e-4569-bc57-441b2cd4aa5b",
  "error": "Error en OpenAI: OpenAI service no está disponible"
}
