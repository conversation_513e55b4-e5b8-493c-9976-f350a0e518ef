// Test del servicio de procesamiento
const processingService = require('./src/services/processingService');

async function testProcessingService() {
  console.log('⚙️ Test del Servicio de Procesamiento\n');
  
  try {
    console.log('1. 🔧 Inicializando servicio...');
    const initialized = await processingService.initialize();
    
    console.log('   ✅ Servicios inicializados:', initialized);
    
    console.log('\n2. 📊 Estadísticas iniciales...');
    const initialStats = processingService.getStats();
    console.log('   📈 Stats:', JSON.stringify(initialStats, null, 2));
    
    console.log('\n3. 🧪 Probando procesamiento de noticia individual...');
    
    // Noticia de prueba
    const testNews = {
      id: 'test-processing-001',
      medio_origen: 'test_medio_processing',
      title: 'Inteligencia Artificial Transforma el Periodismo Digital',
      summary: 'Las nuevas herramientas de IA están revolucionando la forma en que se crean y distribuyen las noticias.',
      content: 'La inteligencia artificial está transformando radicalmente el panorama del periodismo digital. Desde la automatización de la escritura hasta la personalización de contenidos, las herramientas de IA permiten a los medios de comunicación producir contenido de alta calidad de manera más eficiente. Los algoritmos de procesamiento de lenguaje natural pueden analizar grandes volúmenes de información y generar resúmenes precisos, mientras que los sistemas de recomendación personalizan la experiencia de lectura para cada usuario. Esta revolución tecnológica no solo mejora la productividad, sino que también abre nuevas posibilidades para el storytelling y la distribución de noticias.',
      created_at: new Date().toISOString()
    };
    
    console.log('   📰 Noticia de prueba:', {
      id: testNews.id,
      medio: testNews.medio_origen,
      titleLength: testNews.title.length,
      contentLength: testNews.content.length
    });
    
    // Simular procesamiento (sin OpenAI real)
    console.log('\n4. 🔄 Simulando procesamiento...');
    
    const processingResult = await simulateProcessing(testNews);
    
    if (processingResult.success) {
      console.log('   ✅ Procesamiento simulado exitoso');
      console.log('   🆔 Processing ID:', processingResult.data.processingId);
      console.log('   📄 Contenido procesado length:', processingResult.data.processedContent.length);
      console.log('   🔢 Tokens simulados:', processingResult.data.processing.tokensUsed);
      console.log('   ⏱️  Tiempo de procesamiento:', processingResult.data.processing.processingTime, 'ms');
    } else {
      console.log('   ❌ Error en procesamiento:', processingResult.error.message);
    }
    
    console.log('\n5. 📊 Estadísticas después del procesamiento...');
    const statsAfter = processingService.getStats();
    console.log('   📈 Stats actualizadas:', JSON.stringify(statsAfter, null, 2));
    
    console.log('\n6. 🔍 Probando obtención de noticias pendientes...');
    const pendingResult = await processingService.getPendingNews(5);
    
    if (pendingResult.success) {
      console.log('   ✅ Noticias pendientes obtenidas');
      console.log('   📊 Total encontradas:', pendingResult.data.length);
      
      pendingResult.data.forEach((news, index) => {
        console.log(`   ${index + 1}. ${news.medio_origen} - ${news.title.substring(0, 50)}...`);
      });
    } else {
      console.log('   ❌ Error obteniendo noticias pendientes:', pendingResult.error.message);
    }
    
    console.log('\n7. 🔄 Probando procesamiento en lote (simulado)...');
    const batchResult = await simulateBatchProcessing();
    
    if (batchResult.success) {
      console.log('   ✅ Procesamiento en lote simulado');
      console.log('   📊 Total procesadas:', batchResult.data.total);
      console.log('   ✅ Exitosas:', batchResult.data.success);
      console.log('   ❌ Errores:', batchResult.data.errors);
    } else {
      console.log('   ❌ Error en procesamiento en lote:', batchResult.error.message);
    }
    
    console.log('\n8. 📊 Estadísticas finales...');
    const finalStats = processingService.getStats();
    console.log('   📈 Stats finales:', JSON.stringify(finalStats, null, 2));
    
    console.log('\n9. 🔄 Reseteando estadísticas...');
    processingService.resetStats();
    const resetStats = processingService.getStats();
    console.log('   ✅ Stats después del reset:', JSON.stringify(resetStats, null, 2));
    
    console.log('\n✅ Test del servicio de procesamiento completado!');
    
  } catch (error) {
    console.error('\n💥 Error en test:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Simular procesamiento sin llamar a OpenAI real
async function simulateProcessing(newsData) {
  const startTime = Date.now();
  const processingId = `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    console.log('   🎭 Simulando llamada a OpenAI...');
    
    // Simular tiempo de procesamiento
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    // Simular contenido procesado
    const processedContent = `
TÍTULO: ${newsData.title} - Versión Adaptada para ${newsData.medio_origen}

RESUMEN: ${newsData.summary} Esta versión ha sido optimizada para el estilo específico del medio.

CONTENIDO: ${newsData.content}

[CONTENIDO REESCRITO Y ADAPTADO]
Este contenido ha sido procesado y adaptado automáticamente por el sistema de IA para ajustarse al estilo, tono y audiencia específicos del medio "${newsData.medio_origen}". 

La información factual se mantiene intacta mientras que la presentación ha sido optimizada para maximizar el engagement y la comprensión del público objetivo.

[Procesado automáticamente el ${new Date().toLocaleString()}]
    `.trim();
    
    const processingTime = Date.now() - startTime;
    const simulatedTokens = Math.floor(300 + Math.random() * 500);
    
    // Actualizar estadísticas del servicio
    processingService.updateStats({
      success: true,
      tokensUsed: simulatedTokens,
      processingTime
    });
    
    return {
      success: true,
      data: {
        processingId,
        newsId: newsData.id,
        processedContent,
        processing: {
          tokensUsed: simulatedTokens,
          processingTime,
          model: 'gpt-3.5-turbo-simulated',
          timestamp: new Date().toISOString()
        },
        prompt: {
          isCustom: false,
          medio: newsData.medio_origen
        }
      }
    };
    
  } catch (error) {
    const processingTime = Date.now() - startTime;
    
    processingService.updateStats({
      success: false,
      processingTime
    });
    
    return {
      success: false,
      error: {
        code: 'SIMULATION_ERROR',
        message: error.message,
        processingId
      }
    };
  }
}

// Simular procesamiento en lote
async function simulateBatchProcessing() {
  console.log('   🎭 Simulando procesamiento en lote...');
  
  // Simular múltiples noticias
  const mockNews = [
    { id: 'batch-1', medio_origen: 'medio_a', title: 'Noticia 1', content: 'Contenido 1...' },
    { id: 'batch-2', medio_origen: 'medio_b', title: 'Noticia 2', content: 'Contenido 2...' },
    { id: 'batch-3', medio_origen: 'medio_c', title: 'Noticia 3', content: 'Contenido 3...' }
  ];
  
  const results = [];
  
  for (const news of mockNews) {
    // Simular éxito/fallo aleatorio
    const success = Math.random() > 0.2; // 80% de éxito
    
    if (success) {
      const result = await simulateProcessing(news);
      results.push(result);
    } else {
      results.push({
        success: false,
        error: 'Error simulado',
        newsId: news.id
      });
      
      processingService.updateStats({
        success: false,
        processingTime: 1000
      });
    }
  }
  
  const successCount = results.filter(r => r.success).length;
  const errorCount = results.filter(r => !r.success).length;
  
  return {
    success: true,
    data: {
      total: results.length,
      success: successCount,
      errors: errorCount,
      results
    }
  };
}

// Función para mostrar información del servicio
function showServiceInfo() {
  console.log('ℹ️ Información del Servicio de Procesamiento\n');
  
  console.log('🔧 Configuración:');
  console.log('   - Procesamiento concurrente máximo: 3');
  console.log('   - Integración con OpenAI Service');
  console.log('   - Integración con Prompt Service');
  console.log('   - Integración con Database Service');
  
  console.log('\n📊 Funcionalidades:');
  console.log('   ✅ Procesamiento individual de noticias');
  console.log('   ✅ Procesamiento en lote');
  console.log('   ✅ Gestión de cola de procesamiento');
  console.log('   ✅ Estadísticas en tiempo real');
  console.log('   ✅ Manejo de errores y reintentos');
  console.log('   ✅ Rate limiting y control de concurrencia');
  
  console.log('\n🔄 Flujo de Procesamiento:');
  console.log('   1. Crear registro de procesamiento');
  console.log('   2. Obtener prompt personalizado');
  console.log('   3. Procesar con OpenAI');
  console.log('   4. Actualizar registro con resultado');
  console.log('   5. Actualizar estadísticas');
  
  console.log('\n💡 Para probar con OpenAI real:');
  console.log('   1. Configura OPENAI_API_KEY en .env');
  console.log('   2. El servicio detectará automáticamente la key');
  console.log('   3. Ejecuta: node test-processing-service.js');
}

// Ejecutar test apropiado
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--info')) {
    showServiceInfo();
  } else {
    testProcessingService();
  }
}

module.exports = { testProcessingService, simulateProcessing, showServiceInfo };
