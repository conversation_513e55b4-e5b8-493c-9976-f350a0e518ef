#!/bin/bash

# Demo IA Server - Deployment Script
# Uso: ./scripts/deploy.sh [environment] [version]

set -e  # Exit on any error

# Configuración
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT=${1:-production}
VERSION=${2:-latest}
BACKUP_DIR="/backup/demoai"
LOG_FILE="/var/log/demoai/deploy.log"

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funciones de logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

# Verificar prerrequisitos
check_prerequisites() {
    log "Verificando prerrequisitos..."
    
    # Verificar Docker
    if ! command -v docker &> /dev/null; then
        error "Docker no está instalado"
    fi
    
    # Verificar Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose no está instalado"
    fi
    
    # Verificar archivo de entorno
    if [ ! -f "$PROJECT_DIR/.env.$ENVIRONMENT" ]; then
        error "Archivo .env.$ENVIRONMENT no encontrado"
    fi
    
    # Verificar permisos
    if [ ! -w "$PROJECT_DIR" ]; then
        error "Sin permisos de escritura en el directorio del proyecto"
    fi
    
    log "Prerrequisitos verificados ✓"
}

# Crear backup
create_backup() {
    log "Creando backup..."
    
    mkdir -p "$BACKUP_DIR"
    BACKUP_FILE="$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).tar.gz"
    
    # Backup de configuración y logs
    tar -czf "$BACKUP_FILE" \
        -C "$PROJECT_DIR" \
        .env.* \
        docker-compose.yml \
        nginx/ \
        logs/ \
        2>/dev/null || warn "Algunos archivos no pudieron ser respaldados"
    
    log "Backup creado: $BACKUP_FILE"
}

# Pre-deployment checks
pre_deployment_checks() {
    log "Ejecutando verificaciones pre-deployment..."
    
    # Verificar conectividad a servicios externos
    info "Verificando conectividad a Supabase..."
    if ! curl -s --max-time 10 "https://supabase.com" > /dev/null; then
        warn "No se puede conectar a Supabase"
    fi
    
    info "Verificando conectividad a OpenAI..."
    if ! curl -s --max-time 10 "https://api.openai.com" > /dev/null; then
        warn "No se puede conectar a OpenAI API"
    fi
    
    # Verificar espacio en disco
    DISK_USAGE=$(df "$PROJECT_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -gt 80 ]; then
        warn "Uso de disco alto: ${DISK_USAGE}%"
    fi
    
    # Verificar memoria disponible
    MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$MEMORY_USAGE" -gt 80 ]; then
        warn "Uso de memoria alto: ${MEMORY_USAGE}%"
    fi
    
    log "Verificaciones pre-deployment completadas"
}

# Build de la aplicación
build_application() {
    log "Construyendo aplicación..."
    
    cd "$PROJECT_DIR"
    
    # Pull de imágenes base
    docker-compose pull nginx redis prometheus grafana 2>/dev/null || true
    
    # Build de la aplicación
    docker-compose build --no-cache demo-ia-server
    
    # Tag de la imagen con versión
    docker tag "$(docker-compose images -q demo-ia-server)" "demo-ia-server:$VERSION"
    
    log "Aplicación construida exitosamente"
}

# Deploy de la aplicación
deploy_application() {
    log "Desplegando aplicación..."
    
    cd "$PROJECT_DIR"
    
    # Copiar archivo de entorno
    cp ".env.$ENVIRONMENT" .env.production
    
    # Detener servicios existentes
    docker-compose down --remove-orphans
    
    # Iniciar servicios
    if [ "$ENVIRONMENT" = "production" ]; then
        # Producción: solo servicios esenciales
        docker-compose up -d demo-ia-server nginx
    else
        # Desarrollo/staging: todos los servicios
        docker-compose --profile monitoring --profile cache up -d
    fi
    
    log "Aplicación desplegada"
}

# Health checks post-deployment
post_deployment_checks() {
    log "Ejecutando verificaciones post-deployment..."
    
    # Esperar a que la aplicación esté lista
    info "Esperando a que la aplicación esté lista..."
    sleep 30
    
    # Verificar que los contenedores estén corriendo
    if ! docker-compose ps | grep -q "Up"; then
        error "Algunos contenedores no están corriendo"
    fi
    
    # Health check de la aplicación
    info "Verificando health check..."
    for i in {1..10}; do
        if curl -f http://localhost:3000/health > /dev/null 2>&1; then
            log "Health check exitoso ✓"
            break
        fi
        if [ $i -eq 10 ]; then
            error "Health check falló después de 10 intentos"
        fi
        sleep 5
    done
    
    # Verificar endpoints principales
    info "Verificando endpoints principales..."
    
    # Test webhook endpoint (debe retornar 401 sin autenticación)
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/webhook/news | grep -q "401"; then
        log "Webhook endpoint funcionando ✓"
    else
        warn "Webhook endpoint puede tener problemas"
    fi
    
    # Test metrics endpoint
    if curl -f http://localhost:3000/webhook/metrics > /dev/null 2>&1; then
        log "Metrics endpoint funcionando ✓"
    else
        warn "Metrics endpoint puede tener problemas"
    fi
    
    log "Verificaciones post-deployment completadas"
}

# Cleanup de recursos antiguos
cleanup() {
    log "Limpiando recursos antiguos..."
    
    # Limpiar imágenes no utilizadas
    docker image prune -f
    
    # Limpiar volúmenes no utilizados
    docker volume prune -f
    
    # Limpiar backups antiguos (>30 días)
    find "$BACKUP_DIR" -name "backup_*.tar.gz" -mtime +30 -delete 2>/dev/null || true
    
    log "Cleanup completado"
}

# Rollback en caso de error
rollback() {
    error "Deployment falló. Iniciando rollback..."
    
    cd "$PROJECT_DIR"
    
    # Detener servicios actuales
    docker-compose down
    
    # Restaurar desde backup más reciente
    LATEST_BACKUP=$(ls -t "$BACKUP_DIR"/backup_*.tar.gz 2>/dev/null | head -1)
    if [ -n "$LATEST_BACKUP" ]; then
        warn "Restaurando desde backup: $LATEST_BACKUP"
        tar -xzf "$LATEST_BACKUP" -C "$PROJECT_DIR"
    fi
    
    # Intentar iniciar versión anterior
    docker-compose up -d demo-ia-server nginx
    
    error "Rollback completado. Revisa los logs para más detalles."
}

# Función principal
main() {
    log "=== Iniciando deployment de Demo IA Server ==="
    log "Entorno: $ENVIRONMENT"
    log "Versión: $VERSION"
    
    # Configurar trap para rollback en caso de error
    trap rollback ERR
    
    # Ejecutar pasos del deployment
    check_prerequisites
    create_backup
    pre_deployment_checks
    build_application
    deploy_application
    post_deployment_checks
    cleanup
    
    log "=== Deployment completado exitosamente ==="
    
    # Mostrar información útil
    info "Información del deployment:"
    info "- Aplicación: http://localhost:3000"
    info "- Health check: http://localhost:3000/health"
    info "- Métricas: http://localhost:3000/webhook/metrics"
    info "- Logs: docker-compose logs -f demo-ia-server"
    
    if docker-compose ps | grep -q grafana; then
        info "- Grafana: http://localhost:3001 (admin/admin123)"
    fi
    
    if docker-compose ps | grep -q prometheus; then
        info "- Prometheus: http://localhost:9090"
    fi
}

# Verificar argumentos
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Uso: $0 [environment] [version]"
    echo ""
    echo "Argumentos:"
    echo "  environment  Entorno de deployment (production, staging, development)"
    echo "  version      Versión a desplegar (default: latest)"
    echo ""
    echo "Ejemplos:"
    echo "  $0 production v1.0.0"
    echo "  $0 staging latest"
    echo "  $0 development"
    exit 0
fi

# Ejecutar deployment
main "$@"
