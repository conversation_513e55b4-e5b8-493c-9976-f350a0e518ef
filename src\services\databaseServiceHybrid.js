// Servicio híbrido: SQL directo para escritura, cliente para lectura
const { getSupabaseClient } = require('../config/database');
const { logger, logError } = require('../utils/logger');

class DatabaseServiceHybrid {
  constructor() {
    this.client = null;
  }

  initialize(useServiceRole = true) {
    try {
      this.client = getSupabaseClient(useServiceRole);
      return true;
    } catch (error) {
      logError(error, { context: 'DatabaseServiceHybrid.initialize' });
      return false;
    }
  }

  // Escapar strings para SQL
  escapeString(str) {
    if (!str) return 'NULL';
    return `'${str.replace(/'/g, "''")}'`;
  }

  // Convertir array a formato PostgreSQL
  arrayToPostgres(arr) {
    if (!arr || !Array.isArray(arr) || arr.length === 0) return 'NULL';
    const escapedItems = arr.map(item => `"${item.replace(/"/g, '\\"')}"`);
    return `'{${escapedItems.join(',')}}'`;
  }

  // Crear diario usando cliente con configuración optimizada
  async createDiario(diarioData) {
    try {
      if (!this.client) this.initialize();

      logger.info('Creando diario con cliente optimizado', {
        medio_origen: diarioData.medio_origen,
        title: diarioData.title
      });

      // Preparar datos con validación
      const insertData = {
        medio_origen: diarioData.medio_origen,
        header: diarioData.header,
        title: diarioData.title,
        summary: diarioData.summary || null,
        content: diarioData.content,
        images_url: diarioData.images_url || null,
        categories: diarioData.categories || null
      };

      logger.info('Datos preparados para inserción', {
        insertData: {
          ...insertData,
          content: insertData.content.substring(0, 100) + '...'
        }
      });

      // Intentar inserción con diferentes enfoques
      let result;

      // Enfoque 1: Inserción estándar
      try {
        const { data, error } = await this.client
          .from('diarios')
          .insert([insertData])
          .select()
          .single();

        if (error) {
          logger.warn('Inserción estándar falló, intentando alternativa', {
            error: error.message,
            code: error.code,
            details: error.details,
            hint: error.hint,
            fullError: JSON.stringify(error, null, 2)
          });
          throw error;
        }

        result = { data, error: null };
      } catch (insertError) {
        // Enfoque 2: Inserción sin select
        logger.info('Intentando inserción sin select');

        const { data, error } = await this.client
          .from('diarios')
          .insert([insertData]);

        if (error) {
          logger.error('Inserción sin select también falló', {
            error: error.message,
            code: error.code,
            details: error.details,
            hint: error.hint,
            fullError: JSON.stringify(error, null, 2)
          });
          throw error;
        }

        // Si la inserción fue exitosa pero no hay data, crear respuesta básica
        result = {
          data: {
            id: 'inserted-successfully',
            medio_origen: insertData.medio_origen,
            title: insertData.title,
            created_at: new Date().toISOString()
          },
          error: null
        };
      }

      if (result.error) {
        throw result.error;
      }

      logger.info('Diario creado exitosamente', {
        id: result.data.id,
        medio_origen: result.data.medio_origen,
        title: result.data.title
      });

      return { success: true, data: result.data };

    } catch (error) {
      logger.error('Error en createDiario híbrido', {
        error: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint,
        stack: error.stack,
        diarioData: {
          medio_origen: diarioData.medio_origen,
          title: diarioData.title
        }
      });

      return {
        success: false,
        error: {
          code: 'HYBRID_INSERT_ERROR',
          message: error.message,
          details: error.details,
          originalError: error
        }
      };
    }
  }

  // Obtener diario por ID (usando cliente)
  async getDiarioById(id) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('diarios')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: { 
          code: error.code || 'QUERY_ERROR',
          message: error.message 
        }
      };
    }
  }

  // Obtener diarios por medio (usando cliente)
  async getDiariosByMedio(medioOrigen, limit = 10) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('diarios')
        .select('*')
        .eq('medio_origen', medioOrigen)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: { 
          code: error.code || 'QUERY_ERROR',
          message: error.message 
        }
      };
    }
  }

  // Crear registro de procesamiento usando cliente
  async createNewsProcessing(diarioId, status = 'pending') {
    try {
      if (!this.client) this.initialize();

      const insertData = {
        diario_id: diarioId,
        status: status
      };

      const { data, error } = await this.client
        .from('news_processing')
        .insert([insertData])
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info('Registro de procesamiento creado', {
        id: data?.id || 'unknown',
        diario_id: diarioId,
        status
      });

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: error.code || 'PROCESSING_ERROR',
          message: error.message
        }
      };
    }
  }

  // Obtener procesamiento por diario ID (usando cliente)
  async getProcessingByDiarioId(diarioId) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('news_processing')
        .select('*')
        .eq('diario_id', diarioId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: { 
          code: error.code || 'QUERY_ERROR',
          message: error.message 
        }
      };
    }
  }

  // Obtener medios activos (usando cliente)
  async getMediosActivos() {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('medios')
        .select('*')
        .eq('activo', true)
        .order('nombre');

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: { 
          code: error.code || 'QUERY_ERROR',
          message: error.message 
        }
      };
    }
  }
}

// Exportar instancia singleton
const databaseServiceHybrid = new DatabaseServiceHybrid();

module.exports = databaseServiceHybrid;
