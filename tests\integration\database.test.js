const databaseService = require('../../src/services/databaseService');
const { checkTablesExist, testSupabaseConnection } = require('../../src/config/database');

describe('Database Integration Tests', () => {
  beforeAll(async () => {
    // Inicializar servicio de base de datos
    databaseService.initialize();
  });

  describe('Database Connection', () => {
    test('should connect to Supabase', async () => {
      const isConnected = await testSupabaseConnection();
      expect(isConnected).toBe(true);
    });

    test('should check tables exist', async () => {
      const tablesStatus = await checkTablesExist();
      expect(tablesStatus).toHaveProperty('allTablesExist');
      expect(tablesStatus).toHaveProperty('existingTables');
      expect(tablesStatus).toHaveProperty('missingTables');
    });
  });

  describe('Noticias Operations', () => {
    let testNoticiaId;

    const testNoticia = {
      medio_origen: 'test_medio_integration',
      header: 'Header de prueba para test de integración',
      title: 'Título de prueba para test de integración con base de datos',
      summary: 'Resumen de prueba para validar la integración con Supabase',
      content: 'Contenido completo de la noticia de prueba que debe tener al menos 50 caracteres para pasar la validación y ser guardado correctamente en la base de datos Supabase.',
      images_url: ['https://test.com/image1.jpg', 'https://test.com/image2.jpg'],
      categories: ['test', 'integration', 'database']
    };

    test('should create a noticia', async () => {
      const result = await databaseService.createNoticia(testNoticia);
      
      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('id');
      expect(result.data.medio_origen).toBe(testNoticia.medio_origen);
      expect(result.data.title).toBe(testNoticia.title);
      
      testNoticiaId = result.data.id;
    });

    test('should get noticia by id', async () => {
      if (!testNoticiaId) {
        throw new Error('Test noticia not created');
      }

      const result = await databaseService.getNoticiaById(testNoticiaId);
      
      expect(result.success).toBe(true);
      expect(result.data.id).toBe(testNoticiaId);
      expect(result.data.medio_origen).toBe(testNoticia.medio_origen);
    });

    test('should get noticias by medio', async () => {
      const result = await databaseService.getNoticiasByMedio('test_medio_integration');
      
      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('noticias');
      expect(result.data).toHaveProperty('total');
      expect(Array.isArray(result.data.noticias)).toBe(true);
      expect(result.data.total).toBeGreaterThan(0);
    });

    test('should update noticia', async () => {
      if (!testNoticiaId) {
        throw new Error('Test noticia not created');
      }

      const updates = {
        summary: 'Resumen actualizado para test de integración'
      };

      const result = await databaseService.updateNoticia(testNoticiaId, updates);
      
      expect(result.success).toBe(true);
      expect(result.data.summary).toBe(updates.summary);
    });

    test('should handle non-existent noticia', async () => {
      const fakeId = '00000000-0000-0000-0000-000000000000';
      const result = await databaseService.getNoticiaById(fakeId);
      
      expect(result.success).toBe(false);
      expect(result.error).toHaveProperty('code');
    });
  });

  describe('Medios Operations', () => {
    let testMedioId;

    const testMedio = {
      codigo: 'test_medio_db_integration',
      nombre: 'Medio de Prueba DB Integration',
      descripcion: 'Medio creado para tests de integración con base de datos'
    };

    test('should create a medio', async () => {
      const result = await databaseService.createMedio(testMedio);
      
      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('id');
      expect(result.data.codigo).toBe(testMedio.codigo);
      expect(result.data.nombre).toBe(testMedio.nombre);
      
      testMedioId = result.data.id;
    });

    test('should get medio by codigo', async () => {
      const result = await databaseService.getMedioByCodigo(testMedio.codigo);
      
      expect(result.success).toBe(true);
      expect(result.data.codigo).toBe(testMedio.codigo);
      expect(result.data.nombre).toBe(testMedio.nombre);
    });

    test('should get medios activos', async () => {
      const result = await databaseService.getMediosActivos();
      
      expect(result.success).toBe(true);
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data.length).toBeGreaterThan(0);
    });

    test('should handle duplicate codigo', async () => {
      const duplicateMedio = {
        codigo: testMedio.codigo, // Mismo código
        nombre: 'Otro nombre',
        descripcion: 'Otra descripción'
      };

      const result = await databaseService.createMedio(duplicateMedio);
      
      expect(result.success).toBe(false);
      expect(result.error.code).toBe('DUPLICATE_KEY');
    });
  });

  describe('News Processing Operations', () => {
    let testNoticiaId;
    let testProcessingId;

    beforeAll(async () => {
      // Crear una noticia para testing
      const testNoticia = {
        medio_origen: 'test_processing',
        header: 'Header para test de procesamiento',
        title: 'Título para test de procesamiento',
        content: 'Contenido de prueba para test de procesamiento que debe tener al menos 50 caracteres.'
      };

      const result = await databaseService.createNoticia(testNoticia);
      testNoticiaId = result.data.id;
    });

    test('should create news processing record', async () => {
      const result = await databaseService.createNewsProcessing(testNoticiaId, 'pending');
      
      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('id');
      expect(result.data.noticia_id).toBe(testNoticiaId);
      expect(result.data.status).toBe('pending');
      
      testProcessingId = result.data.id;
    });

    test('should update processing status', async () => {
      const updates = {
        status: 'processing',
        openai_tokens_used: 150,
        processing_time_ms: 2500
      };

      const result = await databaseService.updateNewsProcessing(testProcessingId, updates);
      
      expect(result.success).toBe(true);
      expect(result.data.status).toBe('processing');
      expect(result.data.openai_tokens_used).toBe(150);
      expect(result.data.processing_time_ms).toBe(2500);
    });

    test('should get processing by noticia id', async () => {
      const result = await databaseService.getProcessingByNoticiaId(testNoticiaId);
      
      expect(result.success).toBe(true);
      expect(result.data.noticia_id).toBe(testNoticiaId);
      expect(result.data.status).toBe('processing');
    });

    test('should complete processing', async () => {
      const updates = {
        status: 'completed',
        processing_time_ms: 3000
      };

      const result = await databaseService.updateNewsProcessing(testProcessingId, updates);
      
      expect(result.success).toBe(true);
      expect(result.data.status).toBe('completed');
      expect(result.data.completed_at).toBeTruthy();
    });
  });

  describe('System Logs Operations', () => {
    test('should create system log', async () => {
      const result = await databaseService.createSystemLog(
        'info',
        'Test log message',
        { test: true, integration: 'database' }
      );
      
      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('id');
      expect(result.data.level).toBe('info');
      expect(result.data.message).toBe('Test log message');
      expect(result.data.metadata).toEqual({ test: true, integration: 'database' });
    });
  });
});
