# 📚 Documentación Técnica Completa - Demo IA Server

## 🎯 Descripción General

**Demo IA Server** es un sistema completo de procesamiento automatizado de noticias que utiliza inteligencia artificial para reescribir y adaptar contenido periodístico según diferentes estilos de medios de comunicación.

### **Arquitectura del Sistema**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Webhook       │───▶│   Validation     │───▶│   Database      │
│   Endpoint      │    │   & Auth         │    │   (Supabase)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                                               │
         ▼                                               ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Background    │───▶│   OpenAI API     │───▶│   Monitoring    │
│   Processing    │    │   Processing     │    │   & Metrics     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🔧 Componentes Principales

### **1. Webhook Controller (`src/controllers/webhookController.js`)**
- Maneja la recepción de noticias
- Valida datos de entrada
- Inicia procesamiento automático
- Proporciona endpoints de consulta

### **2. OpenAI Service (`src/services/openaiService.js`)**
- Integración con OpenAI GPT-3.5-turbo
- Construcción de prompts dinámicos
- Manejo de rate limiting
- Cálculo de costos y tokens

### **3. Processing Service (`src/services/processingService.js`)**
- Orquesta el procesamiento completo
- Maneja cola de procesamiento
- Procesamiento en lote
- Estadísticas y métricas

### **4. Prompt Service (`src/services/promptService.js`)**
- Gestión de prompts personalizados
- Cache de prompts por medio
- Templates dinámicos
- Fallback a prompts por defecto

### **5. Monitoring Service (`src/services/monitoringService.js`)**
- Métricas en tiempo real
- Monitoreo de salud del sistema
- Tracking de errores
- Recomendaciones automáticas

### **6. Database Service (`src/services/databaseService.js`)**
- Abstracción de Supabase
- Operaciones CRUD optimizadas
- Manejo de conexiones
- Esquemas de datos

## 📊 Esquema de Base de Datos

### **Tabla: `diarios`**
```sql
CREATE TABLE diarios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  medio_origen TEXT NOT NULL,
  header TEXT,
  title TEXT NOT NULL,
  summary TEXT,
  content TEXT NOT NULL,
  images_url TEXT[],
  categories TEXT[],
  source_url TEXT,
  published_date DATE,
  author TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### **Tabla: `news_processing`**
```sql
CREATE TABLE news_processing (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  diario_id UUID REFERENCES diarios(id),
  status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  openai_tokens_used INTEGER,
  processing_time_ms INTEGER,
  error_message TEXT
);
```

### **Tabla: `prompts_medios`**
```sql
CREATE TABLE prompts_medios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  medio_origen TEXT NOT NULL UNIQUE,
  template_prompt TEXT NOT NULL,
  estilo TEXT,
  tono TEXT,
  audiencia TEXT,
  max_palabras INTEGER DEFAULT 500,
  formato_salida TEXT,
  activo BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🌐 API Reference

### **Webhook Endpoints**

#### `POST /webhook/news`
Recibe y procesa una nueva noticia.

**Headers:**
```
Content-Type: application/json
x-webhook-signature: sha256=<hmac_signature>
```

**Body:**
```json
{
  "medio_origen": "string (required)",
  "header": "string (required)",
  "title": "string (required)",
  "summary": "string (optional)",
  "content": "string (required, min 50 chars)",
  "images_url": ["string"] (optional),
  "categories": ["string"] (optional),
  "source_url": "string (optional)",
  "published_date": "YYYY-MM-DD (optional)",
  "author": "string (optional)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Diario recibido y guardado correctamente",
  "data": {
    "id": "uuid",
    "medio_origen": "string",
    "title": "string",
    "created_at": "timestamp",
    "status": "saved",
    "processing_status": "pending",
    "auto_processing": "initiated"
  }
}
```

#### `GET /webhook/news`
Lista noticias recientes con paginación.

**Query Parameters:**
- `limit` (optional): Número de resultados (default: 10)
- `offset` (optional): Offset para paginación (default: 0)
- `medio` (optional): Filtrar por medio específico

#### `GET /webhook/news/:id/status`
Consulta el estado de procesamiento de una noticia.

#### `GET /webhook/news/:id/content`
Obtiene el contenido procesado de una noticia.

#### `POST /webhook/news/:id/process`
Procesa una noticia manualmente (requiere autenticación).

#### `POST /webhook/process-pending`
Procesa noticias pendientes en lote (requiere autenticación).

### **Monitoring Endpoints**

#### `GET /webhook/metrics`
Obtiene métricas del sistema en tiempo real.

#### `GET /webhook/health-report`
Genera reporte completo de salud del sistema.

#### `GET /health`
Health check general del sistema.

## 🔐 Autenticación y Seguridad

### **Webhook Signature Validation**

El sistema utiliza HMAC SHA-256 para validar la autenticidad de los webhooks:

```javascript
const signature = crypto
  .createHmac('sha256', WEBHOOK_SECRET)
  .update(JSON.stringify(payload), 'utf8')
  .digest('hex');

// Header: x-webhook-signature: sha256=<signature>
```

### **Rate Limiting**

- **Webhook endpoints**: 100 requests por 15 minutos
- **Query endpoints**: 200 requests por 15 minutos
- **Processing endpoints**: 50 requests por 15 minutos

### **Validación de Datos**

Todos los endpoints utilizan esquemas Joi para validación:

```javascript
const newsSchema = Joi.object({
  medio_origen: Joi.string().required(),
  header: Joi.string().required(),
  title: Joi.string().required(),
  content: Joi.string().min(50).required(),
  // ... más validaciones
});
```

## 🤖 Integración con OpenAI

### **Configuración**

```env
OPENAI_API_KEY=sk-proj-your-api-key
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.7
```

### **Prompt Template por Defecto**

```
Reescribe la siguiente noticia adaptándola al estilo del medio "{medio}":

NOTICIA ORIGINAL:
Título: {title}
Resumen: {summary}
Contenido: {content}

INSTRUCCIONES:
- Mantén la información factual exacta
- Adapta el tono y estilo al medio especificado
- Conserva la estructura: título, resumen, contenido
- Máximo {maxWords} palabras en el contenido

FORMATO DE RESPUESTA:
TÍTULO: [nuevo título adaptado]
RESUMEN: [nuevo resumen adaptado]
CONTENIDO: [nuevo contenido adaptado]
```

### **Cálculo de Costos**

```javascript
const prices = {
  'gpt-3.5-turbo': 0.002, // por 1K tokens
  'gpt-4': 0.03,
  'gpt-4-turbo': 0.01
};

const cost = (tokens * pricePerToken) / 1000;
```

## 📊 Monitoreo y Métricas

### **Métricas Disponibles**

- **Webhooks**: Total, éxitos, errores, rate de éxito
- **Procesamiento**: Total, éxitos, errores, tokens usados, costos
- **Sistema**: Uptime, memoria, CPU, errores
- **Base de Datos**: Conexiones, tablas, estadísticas

### **Health Checks**

El sistema evalúa automáticamente:
- Rate de éxito de webhooks (>95%)
- Rate de éxito de procesamiento (>90%)
- Uso de memoria (<500MB)
- Errores recientes (<5)

### **Logging**

Logs estructurados con Winston:
- **info**: Operaciones normales
- **warn**: Situaciones de atención
- **error**: Errores del sistema
- **debug**: Información de debugging

## 🚀 Deployment

### **Variables de Entorno de Producción**

```env
NODE_ENV=production
PORT=3000
WEBHOOK_SECRET=super-secure-webhook-secret
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
OPENAI_API_KEY=sk-proj-your-production-key
LOG_LEVEL=info
RATE_LIMIT_MAX_REQUESTS=1000
```

### **Docker Support**

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### **Health Check Endpoint**

```bash
# Para load balancers
curl -f http://localhost:3000/health || exit 1
```

## 🧪 Testing

### **Test Files Disponibles**

- `test-webhook.js` - Test completo del webhook
- `test-openai-service.js` - Test del servicio OpenAI
- `test-prompt-service.js` - Test del servicio de prompts
- `test-processing-service.js` - Test del servicio de procesamiento
- `test-manual-processing.js` - Test de procesamiento manual
- `test-noticia-real.js` - Test con noticia real
- `test-final-demo.js` - Demo completo del sistema

### **Ejecutar Tests**

```bash
# Test individual
node test-webhook.js
node test-openai-service.js

# Demo completo
node test-final-demo.js
```

## 📈 Performance y Escalabilidad

### **Métricas de Performance**

- **Tiempo promedio de procesamiento**: 6.3 segundos
- **Throughput**: ~10 noticias por minuto
- **Memoria promedio**: ~22MB
- **Success rate**: 100%

### **Optimizaciones Implementadas**

- Cache de prompts (5 minutos TTL)
- Procesamiento en background
- Rate limiting inteligente
- Conexiones de BD optimizadas
- Logging asíncrono

### **Escalabilidad**

- **Horizontal**: Múltiples instancias con load balancer
- **Vertical**: Aumentar recursos de CPU/memoria
- **Base de datos**: Supabase escala automáticamente
- **OpenAI**: Rate limiting configurable

## 🔧 Troubleshooting

### **Problemas Comunes**

1. **Error de autenticación webhook**
   - Verificar WEBHOOK_SECRET
   - Validar formato de firma HMAC

2. **Error de conexión a Supabase**
   - Verificar SUPABASE_URL y keys
   - Comprobar conectividad de red

3. **Error de OpenAI API**
   - Verificar OPENAI_API_KEY
   - Comprobar límites de rate limiting

4. **Procesamiento lento**
   - Verificar carga del sistema
   - Optimizar prompts para reducir tokens

### **Logs de Debug**

```bash
# Habilitar logs de debug
LOG_LEVEL=debug npm start

# Ver logs en tiempo real
tail -f ./logs/app.log
```

## 📞 Soporte

Para soporte técnico o reportar issues:
- Revisar logs del sistema
- Verificar métricas en `/webhook/health-report`
- Consultar documentación de APIs
- Ejecutar tests de diagnóstico
