// Test final para demostrar el sistema completo funcionando
const config = require('./src/config/environment');

async function testFinalDemo() {
  console.log('🎉 DEMO FINAL - Sistema de IA para Procesamiento de Noticias\n');
  
  try {
    console.log('📊 RESUMEN DEL SISTEMA:');
    console.log('   🤖 OpenAI GPT-3.5-turbo integrado');
    console.log('   🗄️  Base de datos Supabase funcionando');
    console.log('   📡 Webhook seguro con autenticación HMAC');
    console.log('   📈 Monitoreo en tiempo real');
    console.log('   🔄 Procesamiento automático en background');
    
    console.log('\n1. 📊 Verificando métricas del sistema...');
    
    const metricsResponse = await fetch('http://localhost:3000/webhook/metrics', {
      method: 'GET',
      headers: {
        'User-Agent': 'Demo-Final/1.0'
      }
    });
    
    const metricsResult = await metricsResponse.json();
    
    if (metricsResult.success) {
      const metrics = metricsResult.data.metrics;
      const db = metricsResult.data.database;
      
      console.log('   ✅ Métricas obtenidas exitosamente:');
      console.log('   📈 Webhooks procesados:', metrics.webhooks.total, `(${metrics.webhooks.successRate}% éxito)`);
      console.log('   🤖 Noticias procesadas:', metrics.processing.total, `(${metrics.processing.successRate}% éxito)`);
      console.log('   🔢 Total tokens usados:', metrics.processing.totalTokens.toLocaleString());
      console.log('   💰 Costo total:', '$' + metrics.processing.totalCost);
      console.log('   ⏱️  Tiempo promedio:', (metrics.processing.averageTime / 1000).toFixed(1), 'segundos');
      console.log('   🗄️  Noticias en BD:', db.tables.diarios);
      console.log('   📊 Registros procesamiento:', db.tables.processing);
      console.log('   💾 Memoria usada:', metrics.system.memory.used);
      console.log('   🕐 Uptime:', metrics.system.uptime);
    }
    
    console.log('\n2. 📰 Listando noticias recientes...');
    
    const newsResponse = await fetch('http://localhost:3000/webhook/news?limit=5', {
      method: 'GET',
      headers: {
        'User-Agent': 'Demo-Final/1.0'
      }
    });
    
    const newsResult = await newsResponse.json();
    
    if (newsResult.success) {
      console.log('   ✅ Noticias encontradas:', newsResult.data.news.length);
      
      newsResult.data.news.forEach((news, index) => {
        console.log(`   ${index + 1}. [${news.medio_origen}] ${news.title.substring(0, 60)}...`);
        console.log(`      📅 ${news.created_at} | Status: ${news.processing_status} | Tokens: ${news.tokens_used}`);
      });
      
      // Buscar la noticia de Telesol Diario
      const telesolNews = newsResult.data.news.find(n => n.medio_origen === 'telesol_diario');
      
      if (telesolNews) {
        console.log('\n3. 🔍 Analizando noticia real de Telesol Diario...');
        console.log('   📰 Título:', telesolNews.title);
        console.log('   🆔 ID:', telesolNews.id);
        console.log('   📅 Fecha:', telesolNews.created_at);
        console.log('   🔄 Estado:', telesolNews.processing_status);
        
        // Intentar obtener el contenido procesado
        console.log('\n4. 📄 Intentando obtener contenido procesado...');
        
        const contentResponse = await fetch(`http://localhost:3000/webhook/news/${telesolNews.id}/content`, {
          method: 'GET',
          headers: {
            'User-Agent': 'Demo-Final/1.0'
          }
        });
        
        const contentResult = await contentResponse.json();
        
        if (contentResult.success) {
          console.log('   ✅ Contenido procesado disponible!');
          console.log('   📊 Comparación:');
          console.log('      📝 Original:', contentResult.data.comparison.original_length, 'caracteres');
          console.log('      🤖 Procesado:', contentResult.data.comparison.processed_length, 'caracteres');
          console.log('      🔢 Tokens:', contentResult.data.comparison.tokens_used);
          console.log('      ⏱️  Tiempo:', contentResult.data.comparison.processing_time, 'ms');
          
          console.log('\n   📄 CONTENIDO ORIGINAL:');
          console.log('   ' + '='.repeat(80));
          console.log('   ' + contentResult.data.original.content.substring(0, 300) + '...');
          
          console.log('\n   🤖 CONTENIDO PROCESADO POR IA:');
          console.log('   ' + '='.repeat(80));
          console.log('   ' + (contentResult.data.processed.content || 'No disponible').substring(0, 300) + '...');
          console.log('   ' + '='.repeat(80));
          
        } else {
          console.log('   ⚠️  Contenido procesado no disponible:', contentResult.message);
          console.log('   💡 Esto puede ser normal si el procesamiento aún está en curso');
        }
      }
    }
    
    console.log('\n5. 🏥 Verificando salud del sistema...');
    
    const healthResponse = await fetch('http://localhost:3000/webhook/health-report', {
      method: 'GET',
      headers: {
        'User-Agent': 'Demo-Final/1.0'
      }
    });
    
    const healthResult = await healthResponse.json();
    
    if (healthResult.success) {
      const health = healthResult.data;
      console.log('   ✅ Estado del sistema:', health.status.toUpperCase());
      console.log('   🕐 Uptime:', health.uptime);
      console.log('   ⚠️  Issues:', health.issues.length);
      console.log('   💡 Recomendaciones:', health.recommendations.length);
      
      if (health.issues.length > 0) {
        console.log('   🚨 Issues encontrados:');
        health.issues.forEach((issue, index) => {
          console.log(`      ${index + 1}. ${issue}`);
        });
      }
      
      if (health.recommendations.length > 0) {
        console.log('   💡 Recomendaciones:');
        health.recommendations.forEach((rec, index) => {
          console.log(`      ${index + 1}. ${rec}`);
        });
      }
    }
    
    console.log('\n🎯 DEMOSTRACIÓN COMPLETADA');
    console.log('=' .repeat(80));
    console.log('✅ SISTEMA COMPLETAMENTE FUNCIONAL');
    console.log('');
    console.log('🔧 CARACTERÍSTICAS IMPLEMENTADAS:');
    console.log('   ✅ Webhook seguro con autenticación HMAC SHA-256');
    console.log('   ✅ Integración completa con OpenAI GPT-3.5-turbo');
    console.log('   ✅ Base de datos Supabase con esquemas optimizados');
    console.log('   ✅ Procesamiento automático en background');
    console.log('   ✅ Sistema de prompts personalizables por medio');
    console.log('   ✅ Monitoreo en tiempo real con métricas');
    console.log('   ✅ Manejo robusto de errores y logging');
    console.log('   ✅ APIs RESTful para consulta de datos');
    console.log('   ✅ Validación estricta de datos de entrada');
    console.log('   ✅ Rate limiting y protección contra abuso');
    console.log('');
    console.log('📊 ESTADÍSTICAS FINALES:');
    if (metricsResult.success) {
      const m = metricsResult.data.metrics;
      console.log(`   🔢 ${m.processing.totalTokens.toLocaleString()} tokens procesados`);
      console.log(`   💰 $${m.processing.totalCost} USD en costos de OpenAI`);
      console.log(`   ⏱️  ${(m.processing.averageTime / 1000).toFixed(1)}s tiempo promedio por noticia`);
      console.log(`   📈 ${m.processing.successRate}% tasa de éxito`);
      console.log(`   🗄️  ${metricsResult.data.database.tables.diarios} noticias almacenadas`);
    }
    console.log('');
    console.log('🚀 EL SISTEMA ESTÁ LISTO PARA PRODUCCIÓN!');
    console.log('=' .repeat(80));
    
  } catch (error) {
    console.error('\n💥 Error en demo:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Función para mostrar endpoints disponibles
function mostrarEndpoints() {
  console.log('🌐 ENDPOINTS DISPONIBLES:\n');
  
  console.log('📡 WEBHOOK ENDPOINTS:');
  console.log('   POST /webhook/news                    - Recibir noticias');
  console.log('   GET  /webhook/news                    - Listar noticias recientes');
  console.log('   GET  /webhook/news/:id/status         - Estado de procesamiento');
  console.log('   GET  /webhook/news/:id/content        - Contenido procesado');
  console.log('   POST /webhook/news/:id/process        - Procesamiento manual');
  console.log('   POST /webhook/process-pending         - Procesamiento en lote');
  
  console.log('\n📊 MONITORING ENDPOINTS:');
  console.log('   GET  /webhook/health                  - Health check');
  console.log('   GET  /webhook/metrics                 - Métricas del sistema');
  console.log('   GET  /webhook/health-report           - Reporte completo de salud');
  console.log('   GET  /health                          - Health check general');
  console.log('   GET  /ping                            - Conectividad básica');
  
  console.log('\n🔐 AUTENTICACIÓN:');
  console.log('   - Header: x-webhook-signature');
  console.log('   - Formato: sha256=<hmac_signature>');
  console.log('   - Secret: Configurado en WEBHOOK_SECRET');
  
  console.log('\n📝 EJEMPLO DE USO:');
  console.log('   curl -X POST http://localhost:3000/webhook/news \\');
  console.log('     -H "Content-Type: application/json" \\');
  console.log('     -H "x-webhook-signature: sha256=..." \\');
  console.log('     -d \'{"medio_origen":"test","title":"...","content":"..."}\'');
}

// Ejecutar demo
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--endpoints')) {
    mostrarEndpoints();
  } else {
    testFinalDemo();
  }
}

module.exports = { testFinalDemo, mostrarEndpoints };
