// Test específico para instalación self-hosted de Supabase
const { createClient } = require('@supabase/supabase-js');
const config = require('./src/config/environment');

async function testSelfHostedFix() {
  console.log('🔧 Test de Solución para Supabase Self-Hosted\n');
  
  try {
    console.log('1. 🔍 Configuración actual:');
    console.log('   URL:', config.supabase.url);
    console.log('   Service Key:', config.supabase.serviceRoleKey.substring(0, 20) + '...');
    
    // Configuración específica para self-hosted
    const client = createClient(
      config.supabase.url,
      config.supabase.serviceRoleKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        },
        global: {
          headers: {
            'Authorization': `Bearer ${config.supabase.serviceRoleKey}`,
            'apikey': config.supabase.serviceRoleKey,
            'Content-Type': 'application/json',
            'Prefer': 'return=representation',
            'Accept': 'application/json',
            'Accept-Profile': 'public',
            'Range-Unit': 'items'
          }
        },
        realtime: {
          enabled: false
        }
      }
    );
    
    console.log('\n2. 🔍 Verificando estado de RLS...');
    
    // Test 1: Verificar RLS
    try {
      const { data: rlsData, error: rlsError } = await client
        .from('pg_tables')
        .select('tablename, rowsecurity')
        .eq('schemaname', 'public')
        .eq('tablename', 'diarios');
      
      if (rlsError) {
        console.log('   ⚠️  No se puede verificar RLS:', rlsError.message);
      } else {
        console.log('   ✅ Estado RLS:', rlsData);
      }
    } catch (error) {
      console.log('   ⚠️  Error verificando RLS:', error.message);
    }
    
    console.log('\n3. 🔍 Test de inserción simple...');
    
    // Test 2: Inserción muy básica
    const testData = {
      medio_origen: 'test_selfhosted',
      header: 'Test Self-Hosted',
      title: 'Test de inserción para self-hosted',
      content: 'Contenido de prueba para verificar que la inserción funciona en instalación self-hosted de Supabase.'
    };
    
    console.log('   📝 Datos de prueba:', testData);
    
    // Método 1: Inserción con select
    try {
      const { data, error } = await client
        .from('diarios')
        .insert([testData])
        .select()
        .single();
      
      if (error) {
        console.log('   ❌ Método 1 falló:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        });
        
        // Método 2: Inserción sin select
        console.log('\n4. 🔍 Probando inserción sin select...');
        
        const { data: data2, error: error2 } = await client
          .from('diarios')
          .insert([testData]);
        
        if (error2) {
          console.log('   ❌ Método 2 también falló:', {
            message: error2.message,
            code: error2.code,
            details: error2.details,
            hint: error2.hint
          });
          
          // Método 3: Upsert
          console.log('\n5. 🔍 Probando upsert...');
          
          const { data: data3, error: error3 } = await client
            .from('diarios')
            .upsert([testData])
            .select()
            .single();
          
          if (error3) {
            console.log('   ❌ Método 3 (upsert) también falló:', {
              message: error3.message,
              code: error3.code,
              details: error3.details,
              hint: error3.hint
            });
          } else {
            console.log('   ✅ Método 3 (upsert) FUNCIONÓ!', data3.id);
          }
        } else {
          console.log('   ✅ Método 2 (sin select) FUNCIONÓ!');
        }
      } else {
        console.log('   ✅ Método 1 (con select) FUNCIONÓ!', data.id);
      }
    } catch (error) {
      console.log('   💥 Error general:', error.message);
    }
    
    console.log('\n6. 🔍 Verificando datos existentes...');
    
    // Test 3: Consultar datos
    try {
      const { data: allData, error: selectError } = await client
        .from('diarios')
        .select('id, medio_origen, title, created_at')
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (selectError) {
        console.log('   ❌ Error consultando:', selectError.message);
      } else {
        console.log('   ✅ Datos encontrados:', allData.length, 'registros');
        allData.forEach((item, index) => {
          console.log(`      ${index + 1}. ${item.medio_origen} - ${item.title}`);
        });
      }
    } catch (error) {
      console.log('   💥 Error en consulta:', error.message);
    }
    
    console.log('\n7. 📋 Recomendaciones:');
    console.log('   1. Ejecutar: ALTER TABLE diarios DISABLE ROW LEVEL SECURITY;');
    console.log('   2. Verificar configuración de PostgREST en docker-compose.yml');
    console.log('   3. Reiniciar servicios: docker compose restart');
    console.log('   4. Si persiste, usar método de upsert en lugar de insert');
    
  } catch (error) {
    console.error('\n💥 Error general:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Ejecutar test
if (require.main === module) {
  testSelfHostedFix();
}

module.exports = { testSelfHostedFix };
