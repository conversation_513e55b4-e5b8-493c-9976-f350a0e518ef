// Test del servicio de prompts
const promptService = require('./src/services/promptService');

async function testPromptService() {
  console.log('📝 Test del Servicio de Prompts\n');
  
  try {
    console.log('1. 🔧 Inicializando servicio...');
    const initialized = promptService.initialize();
    
    if (!initialized) {
      console.log('   ❌ No se pudo inicializar el servicio de prompts');
      return;
    }
    
    console.log('   ✅ Servicio inicializado correctamente');
    
    console.log('\n2. 🔍 Obteniendo prompt por defecto...');
    const defaultPrompt = promptService.getDefaultPrompt('test_medio');
    
    console.log('   ✅ Prompt por defecto generado');
    console.log('   📄 Template length:', defaultPrompt.template.length);
    console.log('   ⚙️  Config:', defaultPrompt.config);
    console.log('   🎯 Es personalizado:', defaultPrompt.isCustom);
    
    console.log('\n3. 🔍 Probando obtención desde BD...');
    const promptResult = await promptService.getPromptForMedio('test_medio');
    
    if (!promptResult.success) {
      console.log('   ❌ Error obteniendo prompt:', promptResult.error);
      return;
    }
    
    console.log('   ✅ Prompt obtenido exitosamente');
    console.log('   🎯 Es personalizado:', promptResult.data.isCustom);
    console.log('   📄 Template preview:', promptResult.data.template.substring(0, 100) + '...');
    
    if (promptResult.warning) {
      console.log('   ⚠️  Warning:', promptResult.warning);
    }
    
    console.log('\n4. 💾 Probando guardado de prompt personalizado...');
    
    const customPromptData = {
      template: `
Reescribe esta noticia para el medio "{medio}" con estilo {style}:

ORIGINAL:
Título: {title}
Contenido: {content}

INSTRUCCIONES ESPECÍFICAS:
- Adapta al público {audience}
- Usa tono {tone}
- Máximo {maxWords} palabras
- Formato: {format}

RESULTADO:
TÍTULO: [título adaptado]
CONTENIDO: [contenido reescrito]
      `.trim(),
      config: {
        style: 'dinámico y moderno',
        tone: 'cercano y profesional',
        audience: 'jóvenes profesionales',
        maxWords: '400',
        format: 'título y contenido'
      }
    };
    
    console.log('   📝 Datos del prompt personalizado:', {
      templateLength: customPromptData.template.length,
      config: customPromptData.config
    });
    
    const saveResult = await promptService.savePromptForMedio('test_medio_custom', customPromptData);
    
    if (!saveResult.success) {
      console.log('   ❌ Error guardando prompt:', saveResult.error.message);
    } else {
      console.log('   ✅ Prompt guardado exitosamente');
      console.log('   🔄 Acción:', saveResult.action);
      console.log('   🆔 ID:', saveResult.data.id);
    }
    
    console.log('\n5. 🔍 Verificando prompt guardado...');
    const savedPromptResult = await promptService.getPromptForMedio('test_medio_custom');
    
    if (savedPromptResult.success) {
      console.log('   ✅ Prompt personalizado recuperado');
      console.log('   🎯 Es personalizado:', savedPromptResult.data.isCustom);
      console.log('   ⚙️  Config recuperada:', savedPromptResult.data.config);
    } else {
      console.log('   ❌ Error recuperando prompt guardado');
    }
    
    console.log('\n6. 📋 Listando todos los prompts activos...');
    const allPromptsResult = await promptService.getAllActivePrompts();
    
    if (allPromptsResult.success) {
      console.log('   ✅ Prompts activos obtenidos');
      console.log('   📊 Total:', allPromptsResult.data.length);
      
      allPromptsResult.data.forEach((prompt, index) => {
        console.log(`   ${index + 1}. ${prompt.medio_origen} - ${prompt.estilo} (${prompt.tono})`);
      });
    } else {
      console.log('   ❌ Error obteniendo prompts activos');
    }
    
    console.log('\n7. 📊 Estadísticas del cache...');
    const cacheStats = promptService.getCacheStats();
    console.log('   📈 Tamaño del cache:', cacheStats.size);
    console.log('   🔑 Keys en cache:', cacheStats.keys);
    console.log('   ⏰ Tiempo de expiración:', cacheStats.expiryTime / 1000, 'segundos');
    
    console.log('\n8. 🧹 Probando limpieza de cache...');
    promptService.clearCache();
    const cacheStatsAfter = promptService.getCacheStats();
    console.log('   ✅ Cache limpiado');
    console.log('   📈 Tamaño después:', cacheStatsAfter.size);
    
    console.log('\n9. 🔄 Probando cache después de limpieza...');
    const promptAfterClear = await promptService.getPromptForMedio('test_medio');
    
    if (promptAfterClear.success) {
      console.log('   ✅ Prompt obtenido después de limpiar cache');
      console.log('   📊 Cache size ahora:', promptService.getCacheStats().size);
    }
    
    console.log('\n✅ Test del servicio de prompts completado exitosamente!');
    
  } catch (error) {
    console.error('\n💥 Error en test:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Función para mostrar ejemplo de prompt construido
function showPromptExample() {
  console.log('📄 Ejemplo de Prompt Construido\n');
  
  const defaultPrompt = promptService.getDefaultPrompt('ejemplo_medio');
  
  // Datos de noticia de ejemplo
  const newsData = {
    title: 'Nueva Tecnología Revoluciona el Sector Energético',
    summary: 'Innovación en energías renovables promete reducir costos significativamente',
    content: 'Una nueva tecnología desarrollada por investigadores ha demostrado la capacidad de aumentar la eficiencia de los paneles solares en un 40%. Este avance podría revolucionar el sector energético y acelerar la transición hacia fuentes de energía más limpias.'
  };
  
  // Simular construcción de prompt (como lo haría OpenAI service)
  let prompt = defaultPrompt.template
    .replace(/{title}/g, newsData.title)
    .replace(/{summary}/g, newsData.summary)
    .replace(/{content}/g, newsData.content)
    .replace(/{medio}/g, 'ejemplo_medio')
    .replace(/{maxWords}/g, defaultPrompt.config.maxWords)
    .replace(/{style}/g, defaultPrompt.config.style)
    .replace(/{tone}/g, defaultPrompt.config.tone)
    .replace(/{audience}/g, defaultPrompt.config.audience);
  
  console.log('🔧 PROMPT CONSTRUIDO:');
  console.log('='.repeat(60));
  console.log(prompt);
  console.log('='.repeat(60));
  
  console.log('\n📊 Estadísticas:');
  console.log('   📏 Longitud total:', prompt.length, 'caracteres');
  console.log('   📝 Palabras aprox:', prompt.split(' ').length);
  console.log('   🎯 Medio objetivo:', 'ejemplo_medio');
}

// Ejecutar test apropiado
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--example')) {
    showPromptExample();
  } else {
    testPromptService();
  }
}

module.exports = { testPromptService, showPromptExample };
