// Test directo de inserción en Supabase
const { getSupabaseClient } = require('./src/config/database');

async function testDirectInsert() {
  console.log('🧪 Test Directo de Inserción en Supabase\n');
  
  try {
    const client = getSupabaseClient(true);
    
    console.log('1. 🔍 Verificando estructura de tabla diarios...');
    
    // Test 1: Ver estructura de la tabla
    const { data: columns, error: columnsError } = await client
      .rpc('exec_sql', { 
        sql_query: `
          SELECT column_name, data_type, is_nullable, column_default
          FROM information_schema.columns
          WHERE table_schema = 'public' 
            AND table_name = 'diarios'
          ORDER BY ordinal_position;
        ` 
      });
    
    if (columnsError) {
      console.log('   ⚠️  No se puede obtener estructura (normal)');
    } else {
      console.log('   ✅ Estructura de tabla:', columns);
    }
    
    console.log('\n2. 🔍 Probando inserción simple...');
    
    // Test 2: Inserción muy simple
    const simpleData = {
      medio_origen: 'test_simple',
      header: 'Header simple',
      title: 'Título simple de prueba',
      content: 'Contenido simple de prueba que tiene más de 50 caracteres para cumplir con la validación mínima.'
    };
    
    console.log('   📝 Datos simples:', simpleData);
    
    const { data: insertData, error: insertError } = await client
      .from('diarios')
      .insert([simpleData])
      .select()
      .single();
    
    if (insertError) {
      console.log('   ❌ Error en inserción simple:');
      console.log('      - Mensaje:', insertError.message);
      console.log('      - Código:', insertError.code);
      console.log('      - Detalles:', insertError.details);
      console.log('      - Hint:', insertError.hint);
      console.log('      - Error completo:', JSON.stringify(insertError, null, 2));
    } else {
      console.log('   ✅ Inserción simple exitosa:', insertData.id);
    }
    
    console.log('\n3. 🔍 Probando inserción completa...');
    
    // Test 3: Inserción completa como en el webhook
    const completeData = {
      medio_origen: 'test_completo',
      header: 'Header completo de prueba',
      title: 'Título completo de prueba para test directo',
      summary: 'Resumen de prueba',
      content: 'Contenido completo de prueba que incluye toda la información necesaria para validar el funcionamiento del sistema.',
      images_url: ['https://ejemplo.com/imagen1.jpg'],
      categories: ['test', 'prueba'],
      created_at: new Date().toISOString()
    };
    
    console.log('   📝 Datos completos:', JSON.stringify(completeData, null, 2));
    
    const { data: completeInsertData, error: completeInsertError } = await client
      .from('diarios')
      .insert([completeData])
      .select()
      .single();
    
    if (completeInsertError) {
      console.log('   ❌ Error en inserción completa:');
      console.log('      - Mensaje:', completeInsertError.message);
      console.log('      - Código:', completeInsertError.code);
      console.log('      - Detalles:', completeInsertError.details);
      console.log('      - Hint:', completeInsertError.hint);
      console.log('      - Error completo:', JSON.stringify(completeInsertError, null, 2));
    } else {
      console.log('   ✅ Inserción completa exitosa:', completeInsertData.id);
    }
    
    console.log('\n4. 🔍 Verificando datos insertados...');
    
    // Test 4: Consultar datos insertados
    const { data: allData, error: selectError } = await client
      .from('diarios')
      .select('*')
      .limit(5);
    
    if (selectError) {
      console.log('   ❌ Error consultando datos:', selectError.message);
    } else {
      console.log('   ✅ Datos en tabla diarios:', allData.length, 'registros');
      if (allData.length > 0) {
        console.log('   📄 Último registro:', {
          id: allData[allData.length - 1].id,
          medio_origen: allData[allData.length - 1].medio_origen,
          title: allData[allData.length - 1].title
        });
      }
    }
    
  } catch (error) {
    console.error('\n💥 Error general:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Ejecutar test
if (require.main === module) {
  testDirectInsert();
}

module.exports = { testDirectInsert };
