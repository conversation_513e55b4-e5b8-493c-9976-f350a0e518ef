# 🚀 Guía de Deployment - Demo IA Server

## 📋 Checklist Pre-Deployment

### **✅ Requisitos del Sistema**
- [ ] Node.js 18+ instalado
- [ ] Cuenta de Supabase configurada
- [ ] API Key de OpenAI válida
- [ ] Dominio/servidor de producción
- [ ] SSL/TLS configurado
- [ ] Monitoreo configurado

### **✅ Configuración de Seguridad**
- [ ] Variables de entorno seguras
- [ ] Webhook secret fuerte (32+ caracteres)
- [ ] Rate limiting configurado
- [ ] Firewall configurado
- [ ] Logs de seguridad habilitados

## 🌐 Deployment en Diferentes Plataformas

### **1. Deployment en VPS/Servidor Dedicado**

#### **Preparación del Servidor**
```bash
# Actualizar sistema
sudo apt update && sudo apt upgrade -y

# Instalar Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Instalar PM2 para gestión de procesos
sudo npm install -g pm2

# Crear usuario para la aplicación
sudo useradd -m -s /bin/bash demoai
sudo usermod -aG sudo demoai
```

#### **Configuración de la Aplicación**
```bash
# Cambiar al usuario de la aplicación
sudo su - demoai

# Clonar repositorio
git clone <repository-url> demo-ia-server
cd demo-ia-server

# Instalar dependencias
npm ci --only=production

# Configurar variables de entorno
cp .env.example .env.production
nano .env.production
```

#### **Variables de Entorno de Producción**
```env
# Server Configuration
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# Security
WEBHOOK_SECRET=super-secure-webhook-secret-32-chars-min

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# OpenAI Configuration
OPENAI_API_KEY=sk-proj-your-production-api-key
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.7

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/demoai/app.log

# Rate Limiting (Producción)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
```

#### **Configuración de PM2**
```bash
# Crear archivo de configuración PM2
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'demo-ia-server',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_file: '/var/log/demoai/combined.log',
    out_file: '/var/log/demoai/out.log',
    error_file: '/var/log/demoai/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    max_memory_restart: '500M',
    restart_delay: 4000
  }]
};
EOF

# Crear directorio de logs
sudo mkdir -p /var/log/demoai
sudo chown demoai:demoai /var/log/demoai

# Iniciar aplicación
pm2 start ecosystem.config.js --env production

# Configurar PM2 para inicio automático
pm2 startup
pm2 save
```

#### **Configuración de Nginx (Reverse Proxy)**
```nginx
# /etc/nginx/sites-available/demo-ia-server
server {
    listen 80;
    server_name your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL Configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self'" always;
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    location / {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://localhost:3000/health;
        access_log off;
    }
}
```

```bash
# Habilitar sitio
sudo ln -s /etc/nginx/sites-available/demo-ia-server /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### **2. Deployment en Docker**

#### **Dockerfile**
```dockerfile
FROM node:18-alpine

# Crear directorio de trabajo
WORKDIR /app

# Copiar archivos de dependencias
COPY package*.json ./

# Instalar dependencias
RUN npm ci --only=production && npm cache clean --force

# Crear usuario no-root
RUN addgroup -g 1001 -S nodejs
RUN adduser -S demoai -u 1001

# Copiar código fuente
COPY --chown=demoai:nodejs . .

# Crear directorio de logs
RUN mkdir -p logs && chown demoai:nodejs logs

# Cambiar a usuario no-root
USER demoai

# Exponer puerto
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Comando de inicio
CMD ["npm", "start"]
```

#### **docker-compose.yml**
```yaml
version: '3.8'

services:
  demo-ia-server:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - demo-ia-server
    restart: unless-stopped
```

#### **Comandos Docker**
```bash
# Build y deploy
docker-compose up -d --build

# Ver logs
docker-compose logs -f demo-ia-server

# Escalar horizontalmente
docker-compose up -d --scale demo-ia-server=3

# Actualizar aplicación
docker-compose pull
docker-compose up -d --no-deps demo-ia-server
```

### **3. Deployment en Heroku**

#### **Preparación**
```bash
# Instalar Heroku CLI
npm install -g heroku

# Login
heroku login

# Crear aplicación
heroku create your-app-name
```

#### **Procfile**
```
web: npm start
```

#### **Configuración de Variables**
```bash
# Configurar variables de entorno
heroku config:set NODE_ENV=production
heroku config:set WEBHOOK_SECRET=your-webhook-secret
heroku config:set SUPABASE_URL=your-supabase-url
heroku config:set SUPABASE_SERVICE_ROLE_KEY=your-service-key
heroku config:set OPENAI_API_KEY=your-openai-key

# Deploy
git push heroku main
```

### **4. Deployment en Vercel**

#### **vercel.json**
```json
{
  "version": 2,
  "builds": [
    {
      "src": "server.js",
      "use": "@vercel/node"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/server.js"
    }
  ],
  "env": {
    "NODE_ENV": "production"
  }
}
```

#### **Deploy**
```bash
# Instalar Vercel CLI
npm install -g vercel

# Deploy
vercel --prod
```

## 🔧 Configuración Post-Deployment

### **1. Configuración de Monitoreo**

#### **Configurar Logs Centralizados**
```bash
# Logrotate configuration
sudo cat > /etc/logrotate.d/demoai << EOF
/var/log/demoai/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 demoai demoai
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
```

#### **Configurar Alertas**
```bash
# Script de monitoreo
cat > monitor.sh << EOF
#!/bin/bash
HEALTH_URL="https://your-domain.com/health"
WEBHOOK_URL="your-slack-webhook-url"

if ! curl -f $HEALTH_URL > /dev/null 2>&1; then
    curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"🚨 Demo IA Server is DOWN!"}' \
        $WEBHOOK_URL
fi
EOF

# Agregar a crontab
echo "*/5 * * * * /path/to/monitor.sh" | crontab -
```

### **2. Configuración de Backup**

#### **Backup de Configuración**
```bash
# Script de backup
cat > backup.sh << EOF
#!/bin/bash
BACKUP_DIR="/backup/demoai"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup de configuración
tar -czf $BACKUP_DIR/config_$DATE.tar.gz \
    .env.production \
    ecosystem.config.js \
    nginx.conf

# Backup de logs (últimos 7 días)
find /var/log/demoai -name "*.log" -mtime -7 \
    -exec tar -czf $BACKUP_DIR/logs_$DATE.tar.gz {} +

# Limpiar backups antiguos (>30 días)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
EOF

# Ejecutar backup diario
echo "0 2 * * * /path/to/backup.sh" | crontab -
```

### **3. Configuración de SSL/TLS**

#### **Let's Encrypt con Certbot**
```bash
# Instalar Certbot
sudo apt install certbot python3-certbot-nginx

# Obtener certificado
sudo certbot --nginx -d your-domain.com

# Auto-renovación
sudo crontab -e
# Agregar: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Monitoreo de Producción

### **Métricas Clave a Monitorear**

1. **Disponibilidad del Servicio**
   - Uptime > 99.9%
   - Response time < 2s
   - Health check status

2. **Performance de OpenAI**
   - Tokens por minuto
   - Costo por día
   - Rate limiting hits

3. **Base de Datos**
   - Conexiones activas
   - Query performance
   - Storage usage

4. **Sistema**
   - CPU usage < 80%
   - Memory usage < 80%
   - Disk space > 20% free

### **Dashboards Recomendados**

#### **Grafana Dashboard**
```json
{
  "dashboard": {
    "title": "Demo IA Server Monitoring",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])"
          }
        ]
      },
      {
        "title": "OpenAI Tokens Usage",
        "type": "stat",
        "targets": [
          {
            "expr": "openai_tokens_total"
          }
        ]
      }
    ]
  }
}
```

## 🚨 Troubleshooting de Producción

### **Problemas Comunes**

1. **Alta Latencia**
   ```bash
   # Verificar carga del sistema
   top
   htop
   
   # Verificar logs
   pm2 logs
   tail -f /var/log/demoai/error.log
   ```

2. **Errores de OpenAI**
   ```bash
   # Verificar rate limits
   curl -H "Authorization: Bearer $OPENAI_API_KEY" \
        https://api.openai.com/v1/usage
   ```

3. **Problemas de Base de Datos**
   ```bash
   # Verificar conectividad
   curl -X GET "https://your-project.supabase.co/rest/v1/diarios?select=count" \
        -H "apikey: your-anon-key"
   ```

### **Comandos de Emergencia**

```bash
# Restart rápido
pm2 restart demo-ia-server

# Rollback a versión anterior
git checkout previous-stable-tag
pm2 reload demo-ia-server

# Modo de mantenimiento
# Crear archivo maintenance.html en nginx
```

## ✅ Checklist Post-Deployment

- [ ] Aplicación funcionando correctamente
- [ ] SSL/TLS configurado y funcionando
- [ ] Monitoreo configurado
- [ ] Backups programados
- [ ] Logs rotando correctamente
- [ ] Alertas configuradas
- [ ] Documentación actualizada
- [ ] Equipo notificado del deployment
- [ ] Tests de smoke ejecutados
- [ ] Performance baseline establecido
