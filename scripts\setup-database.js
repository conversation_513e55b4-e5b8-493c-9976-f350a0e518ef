const fs = require('fs');
const path = require('path');
const { getSupabaseClient, checkTablesExist, testSupabaseConnection } = require('../src/config/database');
const { logger, logError } = require('../src/utils/logger');

// Función para leer el archivo SQL
function readSQLFile(filename) {
  try {
    const filePath = path.join(__dirname, '..', 'database', filename);
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    throw new Error(`Error leyendo archivo SQL ${filename}: ${error.message}`);
  }
}

// Función para ejecutar SQL en Supabase
async function executeSQLScript(sqlContent) {
  try {
    const client = getSupabaseClient(true);
    
    // Dividir el script en statements individuales
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📝 Ejecutando ${statements.length} statements SQL...`);
    
    let successCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      try {
        // Usar rpc para ejecutar SQL directo
        const { error } = await client.rpc('exec_sql', { sql_query: statement });
        
        if (error) {
          console.warn(`⚠️  Warning en statement ${i + 1}: ${error.message}`);
          errorCount++;
        } else {
          successCount++;
        }
      } catch (err) {
        console.warn(`⚠️  Error en statement ${i + 1}: ${err.message}`);
        errorCount++;
      }
    }
    
    console.log(`✅ Ejecutados: ${successCount} exitosos, ${errorCount} con warnings`);
    return { success: true, successCount, errorCount };
    
  } catch (error) {
    logError(error, { context: 'executeSQLScript' });
    return { success: false, error: error.message };
  }
}

// Función alternativa para crear tablas usando el cliente Supabase
async function createTablesDirectly() {
  try {
    const client = getSupabaseClient(true);
    
    console.log('📝 Creando tablas directamente...');
    
    // Crear tabla noticias
    console.log('📄 Creando tabla noticias...');
    // Nota: Supabase no permite CREATE TABLE directo via cliente JS
    // Necesitamos usar la interfaz web o SQL Editor
    
    console.log('⚠️  Para crear las tablas, necesitas ejecutar el script SQL manualmente en Supabase');
    console.log('📋 Pasos:');
    console.log('1. Ve a tu proyecto Supabase Dashboard');
    console.log('2. Navega a SQL Editor');
    console.log('3. Copia y pega el contenido de database/schema.sql');
    console.log('4. Ejecuta el script');
    
    return { success: false, needsManualSetup: true };
    
  } catch (error) {
    logError(error, { context: 'createTablesDirectly' });
    return { success: false, error: error.message };
  }
}

// Función para verificar configuración de Supabase
async function verifySupabaseConfig() {
  try {
    console.log('🔍 Verificando configuración de Supabase...');

    const connectionOk = await testSupabaseConnection();

    if (connectionOk) {
      console.log('✅ Conexión con Supabase establecida correctamente');
      return true;
    } else {
      throw new Error('Conexión fallida');
    }

  } catch (error) {
    console.error('❌ Error de conexión con Supabase:', error.message);
    console.log('\n📋 Verifica tu configuración:');
    console.log('- SUPABASE_URL en .env');
    console.log('- SUPABASE_SERVICE_ROLE_KEY en .env');
    console.log('- Que el proyecto Supabase esté activo');
    console.log('- Que la URL sea accesible desde tu red');
    return false;
  }
}

// Función para crear datos de prueba
async function createTestData() {
  try {
    console.log('📝 Creando datos de prueba...');
    
    const databaseService = require('../src/services/databaseService');
    
    // Verificar si ya existen medios
    const mediosResult = await databaseService.getMediosActivos();
    
    if (mediosResult.success && mediosResult.data.length > 0) {
      console.log('✅ Datos de prueba ya existen');
      return true;
    }
    
    // Crear medios de prueba
    const mediosPrueba = [
      {
        codigo: 'test_medio',
        nombre: 'Medio de Prueba',
        descripcion: 'Medio utilizado para testing del sistema'
      },
      {
        codigo: 'diario_ejemplo',
        nombre: 'Diario Ejemplo',
        descripcion: 'Diario de noticias generales'
      },
      {
        codigo: 'tech_news',
        nombre: 'Tech News',
        descripcion: 'Medio especializado en tecnología'
      }
    ];
    
    for (const medio of mediosPrueba) {
      const result = await databaseService.createMedio(medio);
      if (result.success) {
        console.log(`✅ Medio creado: ${medio.nombre}`);
      } else {
        console.log(`⚠️  Error creando medio ${medio.nombre}: ${result.error.message}`);
      }
    }
    
    console.log('✅ Datos de prueba creados');
    return true;
    
  } catch (error) {
    console.error('❌ Error creando datos de prueba:', error.message);
    return false;
  }
}

// Función principal
async function setupDatabase() {
  console.log('🚀 Configuración de Base de Datos - Demo IA Server\n');
  
  try {
    // 1. Verificar configuración
    const configOk = await verifySupabaseConfig();
    if (!configOk) {
      process.exit(1);
    }
    
    // 2. Verificar estado actual de las tablas
    console.log('\n🔍 Verificando estado de las tablas...');
    const tablesStatus = await checkTablesExist();
    
    if (tablesStatus.allTablesExist) {
      console.log('✅ Todas las tablas ya existen');
    } else {
      console.log(`📊 Estado de tablas:`);
      console.log(`   - Existentes: ${tablesStatus.existingTables.length}`);
      console.log(`   - Faltantes: ${tablesStatus.missingTables.length}`);
      
      if (tablesStatus.missingTables.length > 0) {
        console.log(`   - Tablas faltantes: ${tablesStatus.missingTables.join(', ')}`);
        
        console.log('\n📋 Para crear las tablas faltantes:');
        console.log('1. Ve a tu Supabase Dashboard');
        console.log('2. Navega a SQL Editor');
        console.log('3. Copia y pega el contenido de database/schema.sql');
        console.log('4. Ejecuta el script');
        console.log('5. Vuelve a ejecutar este script\n');
        
        // Mostrar el contenido del schema para facilitar copy-paste
        try {
          const schemaContent = readSQLFile('schema.sql');
          console.log('📄 Contenido del schema SQL:');
          console.log('=' * 50);
          console.log(schemaContent);
          console.log('=' * 50);
        } catch (error) {
          console.error('Error leyendo schema.sql:', error.message);
        }
        
        return;
      }
    }
    
    // 3. Crear datos de prueba si las tablas existen
    if (tablesStatus.allTablesExist) {
      await createTestData();
    }
    
    console.log('\n🎉 Configuración de base de datos completada!');
    console.log('✅ El sistema está listo para recibir noticias');
    
  } catch (error) {
    console.error('\n💥 Error durante la configuración:', error.message);
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  setupDatabase();
}

module.exports = {
  setupDatabase,
  verifySupabaseConfig,
  createTestData,
  checkTablesExist
};
