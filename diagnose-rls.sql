-- =====================================================
-- DIAGNÓSTICO DE RLS Y PERMISOS
-- Ejecutar para verificar políticas y permisos
-- =====================================================

-- 1. Ver todas las tablas y si tienen RLS habilitado
SELECT 
  schemaname,
  tablename,
  rowsecurity as rls_enabled,
  tableowner
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- 2. Ver políticas RLS existentes
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- 3. Ver rol actual
SELECT current_user, session_user, current_role;

-- 4. Verificar si la tabla diarios existe y su estructura
SELECT 
  column_name, 
  data_type, 
  is_nullable,
  column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
  AND table_name = 'diarios'
ORDER BY ordinal_position;

-- 5. Intentar inserción directa con SQL (bypass RLS)
-- SOLO PARA TESTING - NO USAR EN PRODUCCIÓN
SET row_security = off;
INSERT INTO diarios (medio_origen, header, title, content) 
VALUES ('test_sql', 'Header SQL', 'Título SQL', 'Contenido SQL de prueba con más de 50 caracteres para validación.');
SET row_security = on;

-- 6. Verificar si se insertó
SELECT COUNT(*) as total_diarios FROM diarios;
