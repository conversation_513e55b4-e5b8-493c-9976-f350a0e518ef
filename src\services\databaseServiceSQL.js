// Servicio de base de datos usando SQL directo como workaround
const { getSupabaseClient } = require('../config/database');
const { logger, logError } = require('../utils/logger');

class DatabaseServiceSQL {
  constructor() {
    this.client = null;
  }

  initialize(useServiceRole = true) {
    try {
      this.client = getSupabaseClient(useServiceRole);
      return true;
    } catch (error) {
      logError(error, { context: 'DatabaseServiceSQL.initialize' });
      return false;
    }
  }

  // Crear diario usando SQL directo
  async createDiario(diarioData) {
    try {
      if (!this.client) this.initialize();

      logger.info('Intentando crear diario con SQL directo', {
        medio_origen: diarioData.medio_origen,
        title: diarioData.title
      });

      // Preparar datos para SQL
      const sqlData = {
        medio_origen: diarioData.medio_origen,
        header: diarioData.header,
        title: diarioData.title,
        summary: diarioData.summary || null,
        content: diarioData.content,
        images_url: diarioData.images_url ? `{${diarioData.images_url.map(url => `"${url}"`).join(',')}}` : null,
        categories: diarioData.categories ? `{${diarioData.categories.map(cat => `"${cat}"`).join(',')}}` : null
      };

      // Construir query SQL
      const insertSQL = `
        INSERT INTO diarios (
          medio_origen, 
          header, 
          title, 
          summary, 
          content, 
          images_url, 
          categories,
          created_at,
          updated_at
        ) VALUES (
          '${sqlData.medio_origen.replace(/'/g, "''")}',
          '${sqlData.header.replace(/'/g, "''")}',
          '${sqlData.title.replace(/'/g, "''")}',
          ${sqlData.summary ? `'${sqlData.summary.replace(/'/g, "''")}'` : 'NULL'},
          '${sqlData.content.replace(/'/g, "''")}',
          ${sqlData.images_url ? `'${sqlData.images_url}'` : 'NULL'},
          ${sqlData.categories ? `'${sqlData.categories}'` : 'NULL'},
          NOW(),
          NOW()
        ) RETURNING *;
      `;

      logger.info('Ejecutando SQL directo', { sql: insertSQL.substring(0, 200) + '...' });

      // Ejecutar SQL usando rpc (si está disponible) o query directo
      let result;
      try {
        // Intentar con rpc primero
        const { data, error } = await this.client.rpc('exec_sql', { sql_query: insertSQL });
        if (error) throw error;
        result = { data, error: null };
      } catch (rpcError) {
        // Si rpc falla, intentar con query directo usando from
        logger.warn('RPC no disponible, usando método alternativo');
        
        // Método alternativo: insertar campo por campo
        const { data, error } = await this.client
          .from('diarios')
          .insert([{
            medio_origen: diarioData.medio_origen,
            header: diarioData.header,
            title: diarioData.title,
            summary: diarioData.summary || null,
            content: diarioData.content,
            // Convertir arrays a formato PostgreSQL
            images_url: diarioData.images_url || null,
            categories: diarioData.categories || null
          }])
          .select()
          .single();
        
        result = { data, error };
      }

      if (result.error) {
        logger.error('Error en SQL directo', {
          error: result.error.message,
          code: result.error.code,
          details: result.error.details
        });
        throw result.error;
      }

      const savedData = Array.isArray(result.data) ? result.data[0] : result.data;

      logger.info('Diario creado con SQL directo', {
        id: savedData.id,
        medio_origen: savedData.medio_origen,
        title: savedData.title
      });

      return { success: true, data: savedData };

    } catch (error) {
      logger.error('Error en createDiario SQL', {
        error: error.message,
        stack: error.stack,
        diarioData: {
          medio_origen: diarioData.medio_origen,
          title: diarioData.title
        }
      });
      
      return { 
        success: false, 
        error: { 
          code: 'SQL_ERROR',
          message: error.message,
          originalError: error
        }
      };
    }
  }

  // Obtener diario por ID
  async getDiarioById(id) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('diarios')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: { 
          code: error.code || 'QUERY_ERROR',
          message: error.message 
        }
      };
    }
  }

  // Crear registro de procesamiento
  async createNewsProcessing(diarioId, status = 'pending') {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('news_processing')
        .insert([{
          diario_id: diarioId,
          status,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info('Registro de procesamiento creado', {
        id: data.id,
        diario_id: diarioId,
        status
      });

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: { 
          code: error.code || 'PROCESSING_ERROR',
          message: error.message 
        }
      };
    }
  }

  // Obtener procesamiento por diario ID
  async getProcessingByDiarioId(diarioId) {
    try {
      if (!this.client) this.initialize();

      const { data, error } = await this.client
        .from('news_processing')
        .select('*')
        .eq('diario_id', diarioId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: { 
          code: error.code || 'QUERY_ERROR',
          message: error.message 
        }
      };
    }
  }
}

// Exportar instancia singleton
const databaseServiceSQL = new DatabaseServiceSQL();

module.exports = databaseServiceSQL;
