// Test de procesamiento manual
const crypto = require('crypto');
const config = require('./src/config/environment');

async function testManualProcessing() {
  console.log('🔧 Test de Procesamiento Manual\n');
  
  try {
    // ID de la noticia más reciente
    const newsId = '05598b45-7ee3-4b7b-aca3-e52f4309c757';
    
    console.log('1. 📰 Procesando noticia manualmente...');
    console.log('   ID:', newsId);
    
    // Crear firma para autenticación
    const timestamp = Date.now().toString();
    const payload = `${timestamp}:POST:/webhook/news/${newsId}/process:`;
    const signature = crypto
      .createHmac('sha256', config.webhook.secret)
      .update(payload)
      .digest('hex');
    
    console.log('   🔐 Firma creada para autenticación');
    
    // Realizar request de procesamiento manual
    const response = await fetch(`http://localhost:3000/webhook/news/${newsId}/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Test-Manual-Processing/1.0',
        'x-webhook-signature': `sha256=${signature}`,
        'x-webhook-timestamp': timestamp
      }
    });
    
    const result = await response.json();
    
    console.log('\n2. 📥 Respuesta del procesamiento manual:');
    console.log('   Status:', response.status);
    console.log('   Success:', result.success);
    console.log('   Message:', result.message);
    
    if (result.success) {
      console.log('\n   ✅ Procesamiento exitoso!');
      console.log('   🆔 Processing ID:', result.data.processing.id);
      console.log('   🔢 Tokens usados:', result.data.processing.tokensUsed);
      console.log('   ⏱️  Tiempo:', result.data.processing.processingTime, 'ms');
      console.log('   🤖 Modelo:', result.data.processing.model);
      console.log('   📄 Contenido length:', result.data.processedContent.length);
      console.log('   🎯 Prompt personalizado:', result.data.prompt.isCustom);
      
      console.log('\n   📄 CONTENIDO PROCESADO (primeros 200 chars):');
      console.log('   ' + '='.repeat(60));
      console.log('   ' + result.data.processedContent.substring(0, 200) + '...');
      console.log('   ' + '='.repeat(60));
    } else {
      console.log('\n   ❌ Error en procesamiento:');
      console.log('   Code:', result.error?.code);
      console.log('   Details:', result.error?.details);
    }
    
    console.log('\n3. 🔍 Verificando contenido procesado...');
    
    // Obtener contenido procesado
    const contentResponse = await fetch(`http://localhost:3000/webhook/news/${newsId}/content`, {
      method: 'GET',
      headers: {
        'User-Agent': 'Test-Manual-Processing/1.0'
      }
    });
    
    const contentResult = await contentResponse.json();
    
    console.log('   Status:', contentResponse.status);
    console.log('   Success:', contentResult.success);
    
    if (contentResult.success) {
      console.log('   ✅ Contenido procesado disponible');
      console.log('   📊 Original length:', contentResult.data.comparison.original_length);
      console.log('   📊 Processed length:', contentResult.data.comparison.processed_length);
      console.log('   🔢 Tokens:', contentResult.data.comparison.tokens_used);
    } else {
      console.log('   ⚠️  Contenido no disponible:', contentResult.message);
    }
    
    console.log('\n4. 📊 Verificando métricas actualizadas...');
    
    const metricsResponse = await fetch('http://localhost:3000/webhook/metrics', {
      method: 'GET',
      headers: {
        'User-Agent': 'Test-Manual-Processing/1.0'
      }
    });
    
    const metricsResult = await metricsResponse.json();
    
    if (metricsResult.success) {
      const metrics = metricsResult.data.metrics;
      console.log('   ✅ Métricas obtenidas');
      console.log('   📈 Total procesados:', metrics.processing.total);
      console.log('   ✅ Éxitos:', metrics.processing.success);
      console.log('   ❌ Errores:', metrics.processing.errors);
      console.log('   🔢 Total tokens:', metrics.processing.totalTokens);
      console.log('   💰 Costo total:', '$' + metrics.processing.totalCost);
      console.log('   ⏱️  Tiempo promedio:', metrics.processing.averageTime, 'ms');
      console.log('   📊 Success rate:', metrics.processing.successRate + '%');
    }
    
    console.log('\n✅ Test de procesamiento manual completado!');
    
  } catch (error) {
    console.error('\n💥 Error en test:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Función para probar procesamiento en lote
async function testBatchProcessing() {
  console.log('\n🔄 Test de Procesamiento en Lote\n');
  
  try {
    console.log('1. 🚀 Iniciando procesamiento en lote...');
    
    // Crear firma para autenticación
    const timestamp = Date.now().toString();
    const payload = `${timestamp}:POST:/webhook/process-pending:`;
    const signature = crypto
      .createHmac('sha256', config.webhook.secret)
      .update(payload)
      .digest('hex');
    
    const response = await fetch('http://localhost:3000/webhook/process-pending?limit=3', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Test-Batch-Processing/1.0',
        'x-webhook-signature': `sha256=${signature}`,
        'x-webhook-timestamp': timestamp
      }
    });
    
    const result = await response.json();
    
    console.log('2. 📥 Respuesta del procesamiento en lote:');
    console.log('   Status:', response.status);
    console.log('   Success:', result.success);
    console.log('   Message:', result.message);
    
    if (result.success) {
      console.log('\n   ✅ Procesamiento en lote exitoso!');
      console.log('   📊 Total procesadas:', result.data.total);
      console.log('   ✅ Éxitos:', result.data.success);
      console.log('   ❌ Errores:', result.data.errors);
      
      const stats = result.data.stats;
      console.log('\n   📈 Estadísticas del servicio:');
      console.log('   🔢 Total tokens:', stats.totalTokensUsed);
      console.log('   ⏱️  Tiempo promedio:', stats.averageProcessingTime, 'ms');
      console.log('   📊 Success rate:', stats.successRate + '%');
    } else {
      console.log('\n   ❌ Error en procesamiento en lote:');
      console.log('   Code:', result.error?.code);
      console.log('   Details:', result.error?.details);
    }
    
  } catch (error) {
    console.error('\n💥 Error en test de lote:', error.message);
  }
}

// Ejecutar tests
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--batch')) {
    testBatchProcessing();
  } else if (args.includes('--both')) {
    testManualProcessing().then(() => testBatchProcessing());
  } else {
    testManualProcessing();
  }
}

module.exports = { testManualProcessing, testBatchProcessing };
