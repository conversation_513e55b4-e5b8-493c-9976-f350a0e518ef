const express = require('express');
const router = express.Router();

// Importar controladores
const {
  receiveNewsWebhook,
  getNewsStatus,
  webhookHealthCheck,
  processNewsManually,
  processPendingNews,
  getSystemMetrics,
  getHealthReport,
  getProcessedContent,
  getRecentNews
} = require('../controllers/webhookController');

// Importar middleware
const {
  validateWebhookPayload,
  validateContentType,
  validatePayloadSize,
  sanitizeInput
} = require('../middleware/validation');

const {
  validateWebhookSignature,
  validateOrigin
} = require('../middleware/authentication');

const {
  webhookRateLimiter,
  strictRateLimiter
} = require('../middleware/rateLimiting');

// Middleware común para todas las rutas de webhook
router.use(webhookRateLimiter);

/**
 * POST /webhook/news
 * Endpoint principal para recibir noticias desde el panel de administración
 */
router.post('/news',
  // Middleware de validación y seguridad
  validateContentType,
  validatePayloadSize('10mb'),
  validateOrigin,
  validateWebhookSignature,
  validateWebhookPayload,
  sanitizeInput,
  
  // Controlador principal
  receiveNewsWebhook
);

/**
 * GET /webhook/news/:id/status
 * Endpoint para consultar el estado de procesamiento de una noticia
 */
router.get('/news/:id/status',
  strictRateLimiter, // Rate limiting más estricto para consultas
  getNewsStatus
);

/**
 * GET /webhook/news/:id/content
 * Endpoint para obtener el contenido procesado de una noticia
 */
router.get('/news/:id/content',
  strictRateLimiter,
  getProcessedContent
);

/**
 * GET /webhook/news
 * Endpoint para listar noticias recientes con paginación
 */
router.get('/news',
  strictRateLimiter,
  getRecentNews
);

/**
 * GET /webhook/health
 * Health check específico para el servicio de webhooks
 */
router.get('/health', webhookHealthCheck);

/**
 * GET /webhook/metrics
 * Endpoint para obtener métricas del sistema
 */
router.get('/metrics',
  strictRateLimiter,
  getSystemMetrics
);

/**
 * GET /webhook/health-report
 * Endpoint para obtener reporte completo de salud del sistema
 */
router.get('/health-report',
  strictRateLimiter,
  getHealthReport
);

/**
 * POST /webhook/news/:id/process
 * Endpoint para procesar una noticia específica manualmente
 */
router.post('/news/:id/process',
  strictRateLimiter,
  validateOrigin, // Requiere autenticación para procesamiento manual
  processNewsManually
);

/**
 * POST /webhook/process-pending
 * Endpoint para procesar noticias pendientes en lote
 */
router.post('/process-pending',
  strictRateLimiter,
  validateOrigin, // Requiere autenticación para procesamiento en lote
  processPendingNews
);

/**
 * GET /webhook/ping
 * Endpoint simple para verificar conectividad
 */
router.get('/ping', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'pong',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
