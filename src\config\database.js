const { createClient } = require('@supabase/supabase-js');
const config = require('./environment');
const { logger, logError } = require('../utils/logger');

// Cliente Supabase con configuración de servicio (para operaciones administrativas)
let supabaseServiceClient = null;

// Cliente Supabase con configuración anónima (para operaciones públicas)
let supabaseAnonClient = null;

// Función para inicializar clientes Supabase
function initializeSupabaseClients() {
  try {
    // Cliente con service role key para operaciones administrativas
    if (config.supabase.serviceRoleKey) {
      supabaseServiceClient = createClient(
        config.supabase.url,
        config.supabase.serviceRoleKey,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          },
          db: {
            schema: 'public'
          }
        }
      );
      
      logger.info('Cliente Supabase Service inicializado correctamente');
    }

    // Cliente con anon key para operaciones públicas
    if (config.supabase.anonKey) {
      supabaseAnonClient = createClient(
        config.supabase.url,
        config.supabase.anonKey,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          },
          db: {
            schema: 'public'
          }
        }
      );
      
      logger.info('Cliente Supabase Anon inicializado correctamente');
    }

    return true;
  } catch (error) {
    logError(error, { context: 'initializeSupabaseClients' });
    return false;
  }
}

// Función para verificar conexión con Supabase
async function testSupabaseConnection() {
  try {
    if (!supabaseServiceClient) {
      throw new Error('Cliente Supabase Service no inicializado');
    }

    // Test de conexión muy básico - intentar cualquier consulta
    // Si falla, significa que hay problemas de conectividad o autenticación
    const { data, error } = await supabaseServiceClient
      .from('diarios')
      .select('count', { count: 'exact', head: true });

    // Cualquier respuesta (incluso error de tabla no existe) significa que la conexión funciona
    if (error) {
      // Códigos de error que indican conexión exitosa pero tabla/permisos
      const connectionOkCodes = ['PGRST116', '42P01', 'PGRST204'];

      if (connectionOkCodes.includes(error.code)) {
        logger.info('Conexión con Supabase verificada correctamente (tabla no existe aún)');
        return true;
      }

      // Otros errores indican problemas de conexión/auth
      throw error;
    }

    logger.info('Conexión con Supabase verificada correctamente');
    return true;
  } catch (error) {
    // Errores de red/fetch indican problemas de conectividad
    if (error.message && error.message.includes('fetch failed')) {
      logError(new Error('Error de conectividad con Supabase - verificar URL y red'), {
        context: 'testSupabaseConnection',
        originalError: error.message
      });
    } else {
      logError(error, { context: 'testSupabaseConnection' });
    }
    return false;
  }
}

// Función para obtener cliente según el tipo de operación
function getSupabaseClient(useServiceRole = true) {
  if (useServiceRole) {
    if (!supabaseServiceClient) {
      throw new Error('Cliente Supabase Service no está inicializado');
    }
    return supabaseServiceClient;
  } else {
    if (!supabaseAnonClient) {
      throw new Error('Cliente Supabase Anon no está inicializado');
    }
    return supabaseAnonClient;
  }
}

// Función para manejar errores de Supabase
function handleSupabaseError(error, context = 'unknown') {
  const errorInfo = {
    context,
    code: error.code,
    message: error.message,
    details: error.details,
    hint: error.hint
  };

  logError(error, errorInfo);

  // Mapear errores comunes de Supabase a códigos de aplicación
  const errorMappings = {
    'PGRST116': 'TABLE_NOT_FOUND',
    '23505': 'DUPLICATE_KEY',
    '23503': 'FOREIGN_KEY_VIOLATION',
    '23502': 'NOT_NULL_VIOLATION',
    '42P01': 'UNDEFINED_TABLE',
    '42703': 'UNDEFINED_COLUMN'
  };

  return {
    code: errorMappings[error.code] || 'DATABASE_ERROR',
    message: error.message,
    originalError: error
  };
}

// Función para ejecutar transacciones
async function executeTransaction(operations) {
  const client = getSupabaseClient(true);
  
  try {
    // Supabase maneja transacciones automáticamente en operaciones batch
    const results = [];
    
    for (const operation of operations) {
      const result = await operation(client);
      results.push(result);
    }
    
    return { success: true, results };
  } catch (error) {
    const mappedError = handleSupabaseError(error, 'executeTransaction');
    return { success: false, error: mappedError };
  }
}

// Función para verificar si las tablas existen
async function checkTablesExist() {
  try {
    const client = getSupabaseClient(true);
    
    const tables = ['diarios', 'medios', 'prompts_medios', 'system_logs', 'news_processing'];
    const existingTables = [];
    
    for (const table of tables) {
      try {
        const { error } = await client
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        if (!error) {
          existingTables.push(table);
        }
      } catch (err) {
        // Tabla no existe, continuar
      }
    }
    
    logger.info('Verificación de tablas completada', {
      totalTables: tables.length,
      existingTables: existingTables.length,
      tables: existingTables
    });
    
    return {
      allTablesExist: existingTables.length === tables.length,
      existingTables,
      missingTables: tables.filter(t => !existingTables.includes(t))
    };
  } catch (error) {
    logError(error, { context: 'checkTablesExist' });
    return {
      allTablesExist: false,
      existingTables: [],
      missingTables: [],
      error: error.message
    };
  }
}

// Inicializar clientes al cargar el módulo
const initialized = initializeSupabaseClients();

module.exports = {
  getSupabaseClient,
  testSupabaseConnection,
  handleSupabaseError,
  executeTransaction,
  checkTablesExist,
  initialized
};
