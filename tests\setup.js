// Setup global para tests
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // Reducir logs durante tests
process.env.WEBHOOK_SECRET = 'test_webhook_secret_123';
process.env.SUPABASE_URL = 'https://test.supabase.co';
process.env.SUPABASE_ANON_KEY = 'test_anon_key';
process.env.OPENAI_API_KEY = 'test_openai_key';

// Configurar timeouts globales
jest.setTimeout(10000);

// Mock console para tests más limpios
global.console = {
  ...console,
  // Mantener error y warn para debugging
  log: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
};
