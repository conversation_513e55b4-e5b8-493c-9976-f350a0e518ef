// Servicio de monitoreo y métricas del sistema
const { logger, logError } = require('../utils/logger');
const { getSupabaseClient } = require('../config/database');

class MonitoringService {
  constructor() {
    this.client = null;
    this.metrics = {
      webhooks: {
        total: 0,
        success: 0,
        errors: 0,
        lastHour: 0
      },
      processing: {
        total: 0,
        success: 0,
        errors: 0,
        totalTokens: 0,
        totalCost: 0,
        averageTime: 0
      },
      system: {
        uptime: 0,
        memory: {},
        cpu: 0,
        errors: 0
      }
    };
    this.startTime = Date.now();
    this.errorLog = [];
    this.maxErrorLogSize = 100;
  }

  // Inicializar servicio
  initialize() {
    try {
      this.client = getSupabaseClient(true); // Usar service role
      this.startTime = Date.now();
      
      // Iniciar recolección de métricas periódica
      this.startMetricsCollection();
      
      logger.info('Servicio de monitoreo inicializado', {
        startTime: new Date(this.startTime).toISOString()
      });
      
      return true;
    } catch (error) {
      logError(error, { context: 'MonitoringService.initialize' });
      return false;
    }
  }

  // Iniciar recolección de métricas cada minuto
  startMetricsCollection() {
    setInterval(() => {
      this.collectSystemMetrics();
    }, 60000); // Cada minuto
  }

  // Recopilar métricas del sistema
  collectSystemMetrics() {
    try {
      this.metrics.system.uptime = Date.now() - this.startTime;
      this.metrics.system.memory = process.memoryUsage();
      
      // Log de métricas cada 5 minutos
      if (Math.floor(this.metrics.system.uptime / 60000) % 5 === 0) {
        logger.info('Métricas del sistema', {
          uptime: this.formatUptime(this.metrics.system.uptime),
          memory: this.formatMemory(this.metrics.system.memory),
          webhooks: this.metrics.webhooks,
          processing: this.metrics.processing
        });
      }
    } catch (error) {
      logError(error, { context: 'MonitoringService.collectSystemMetrics' });
    }
  }

  // Registrar webhook recibido
  recordWebhook(success = true, error = null) {
    this.metrics.webhooks.total++;
    
    if (success) {
      this.metrics.webhooks.success++;
    } else {
      this.metrics.webhooks.errors++;
      if (error) {
        this.addError('webhook', error);
      }
    }

    // Incrementar contador de última hora
    this.metrics.webhooks.lastHour++;
    
    // Reset contador cada hora
    setTimeout(() => {
      this.metrics.webhooks.lastHour = Math.max(0, this.metrics.webhooks.lastHour - 1);
    }, 3600000); // 1 hora
  }

  // Registrar procesamiento completado
  recordProcessing(success = true, data = {}) {
    this.metrics.processing.total++;
    
    if (success) {
      this.metrics.processing.success++;
      
      // Actualizar métricas de tokens y costos
      if (data.tokensUsed) {
        this.metrics.processing.totalTokens += data.tokensUsed;
        this.metrics.processing.totalCost += this.calculateCost(data.tokensUsed, data.model);
      }
      
      // Actualizar tiempo promedio
      if (data.processingTime) {
        const currentAvg = this.metrics.processing.averageTime;
        const count = this.metrics.processing.success;
        this.metrics.processing.averageTime = 
          ((currentAvg * (count - 1)) + data.processingTime) / count;
      }
    } else {
      this.metrics.processing.errors++;
      if (data.error) {
        this.addError('processing', data.error);
      }
    }

    logger.info('Procesamiento registrado en métricas', {
      success,
      tokensUsed: data.tokensUsed || 0,
      processingTime: data.processingTime || 0,
      totalProcessed: this.metrics.processing.total
    });
  }

  // Calcular costo estimado basado en tokens y modelo
  calculateCost(tokens, model = 'gpt-3.5-turbo') {
    // Precios aproximados por 1K tokens (actualizar según precios reales)
    const prices = {
      'gpt-3.5-turbo': 0.002,
      'gpt-4': 0.03,
      'gpt-4-turbo': 0.01
    };
    
    const pricePerToken = (prices[model] || prices['gpt-3.5-turbo']) / 1000;
    return tokens * pricePerToken;
  }

  // Agregar error al log
  addError(type, error) {
    const errorEntry = {
      type,
      message: error.message || error,
      timestamp: new Date().toISOString(),
      stack: error.stack || null
    };

    this.errorLog.unshift(errorEntry);
    
    // Mantener solo los últimos N errores
    if (this.errorLog.length > this.maxErrorLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxErrorLogSize);
    }

    this.metrics.system.errors++;
  }

  // Obtener métricas actuales
  getMetrics() {
    return {
      ...this.metrics,
      system: {
        ...this.metrics.system,
        uptime: this.formatUptime(this.metrics.system.uptime),
        memory: this.formatMemory(this.metrics.system.memory),
        startTime: new Date(this.startTime).toISOString()
      },
      processing: {
        ...this.metrics.processing,
        averageTime: Math.round(this.metrics.processing.averageTime),
        totalCost: Math.round(this.metrics.processing.totalCost * 10000) / 10000, // 4 decimales
        successRate: this.metrics.processing.total > 0 
          ? Math.round((this.metrics.processing.success / this.metrics.processing.total) * 100)
          : 0
      },
      webhooks: {
        ...this.metrics.webhooks,
        successRate: this.metrics.webhooks.total > 0 
          ? Math.round((this.metrics.webhooks.success / this.metrics.webhooks.total) * 100)
          : 0
      },
      timestamp: new Date().toISOString()
    };
  }

  // Obtener errores recientes
  getRecentErrors(limit = 10) {
    return this.errorLog.slice(0, limit);
  }

  // Obtener estadísticas de la base de datos
  async getDatabaseStats() {
    try {
      if (!this.client) return null;

      // Contar registros en tablas principales
      const [diarios, processing, logs] = await Promise.all([
        this.client.from('diarios').select('id', { count: 'exact', head: true }),
        this.client.from('news_processing').select('id', { count: 'exact', head: true }),
        this.client.from('system_logs').select('id', { count: 'exact', head: true })
      ]);

      // Estadísticas de procesamiento por estado
      const { data: processingStats } = await this.client
        .from('news_processing')
        .select('status')
        .then(result => {
          if (result.error) return { data: [] };
          
          const stats = result.data.reduce((acc, item) => {
            acc[item.status] = (acc[item.status] || 0) + 1;
            return acc;
          }, {});
          
          return { data: stats };
        });

      return {
        tables: {
          diarios: diarios.count || 0,
          processing: processing.count || 0,
          logs: logs.count || 0
        },
        processingStats: processingStats || {},
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logError(error, { context: 'MonitoringService.getDatabaseStats' });
      return null;
    }
  }

  // Formatear tiempo de uptime
  formatUptime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
    if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }

  // Formatear memoria
  formatMemory(memory) {
    const formatBytes = (bytes) => {
      const mb = bytes / 1024 / 1024;
      return `${Math.round(mb * 100) / 100} MB`;
    };

    return {
      used: formatBytes(memory.heapUsed),
      total: formatBytes(memory.heapTotal),
      external: formatBytes(memory.external),
      rss: formatBytes(memory.rss)
    };
  }

  // Generar reporte de salud del sistema
  async getHealthReport() {
    try {
      const metrics = this.getMetrics();
      const dbStats = await this.getDatabaseStats();
      const recentErrors = this.getRecentErrors(5);

      // Determinar estado de salud
      let healthStatus = 'healthy';
      const issues = [];

      // Verificar rate de errores
      if (metrics.webhooks.successRate < 95) {
        healthStatus = 'warning';
        issues.push(`Webhook success rate bajo: ${metrics.webhooks.successRate}%`);
      }

      if (metrics.processing.successRate < 90) {
        healthStatus = 'warning';
        issues.push(`Processing success rate bajo: ${metrics.processing.successRate}%`);
      }

      // Verificar memoria
      const memoryUsed = metrics.system.memory.used;
      if (memoryUsed.includes('MB')) {
        const mb = parseFloat(memoryUsed.replace(' MB', ''));
        if (mb > 500) {
          healthStatus = 'warning';
          issues.push(`Uso de memoria alto: ${memoryUsed}`);
        }
      }

      // Verificar errores recientes
      if (recentErrors.length > 5) {
        healthStatus = 'critical';
        issues.push(`Muchos errores recientes: ${recentErrors.length}`);
      }

      return {
        status: healthStatus,
        timestamp: new Date().toISOString(),
        uptime: metrics.system.uptime,
        metrics,
        database: dbStats,
        recentErrors,
        issues,
        recommendations: this.generateRecommendations(metrics, issues)
      };

    } catch (error) {
      logError(error, { context: 'MonitoringService.getHealthReport' });
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Generar recomendaciones basadas en métricas
  generateRecommendations(metrics, issues) {
    const recommendations = [];

    if (metrics.processing.totalTokens > 100000) {
      recommendations.push('Considerar optimizar prompts para reducir uso de tokens');
    }

    if (metrics.processing.averageTime > 10000) {
      recommendations.push('Tiempo de procesamiento alto, verificar performance de OpenAI');
    }

    if (issues.length > 0) {
      recommendations.push('Revisar logs de errores y tomar acciones correctivas');
    }

    if (metrics.webhooks.lastHour > 100) {
      recommendations.push('Alto volumen de webhooks, considerar scaling');
    }

    return recommendations;
  }

  // Resetear métricas
  resetMetrics() {
    this.metrics = {
      webhooks: { total: 0, success: 0, errors: 0, lastHour: 0 },
      processing: { total: 0, success: 0, errors: 0, totalTokens: 0, totalCost: 0, averageTime: 0 },
      system: { uptime: Date.now() - this.startTime, memory: {}, cpu: 0, errors: 0 }
    };
    this.errorLog = [];
    
    logger.info('Métricas reseteadas');
  }
}

// Exportar instancia singleton
const monitoringService = new MonitoringService();

module.exports = monitoringService;
