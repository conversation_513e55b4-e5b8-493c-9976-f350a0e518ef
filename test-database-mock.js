// Test de funcionalidad de base de datos con mocks
// Para probar la lógica sin necesidad de Supabase real

const { createApiResponse } = require('./src/models/newsSchema');

// Mock del servicio de base de datos
class MockDatabaseService {
  constructor() {
    this.diarios = new Map();
    this.medios = new Map();
    this.processing = new Map();
    this.logs = [];
    this.nextId = 1;
  }

  generateId() {
    return `mock-id-${this.nextId++}`;
  }

  async createDiario(diarioData) {
    try {
      const id = this.generateId();
      const diario = {
        id,
        ...diarioData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      this.diarios.set(id, diario);

      console.log(`✅ Mock: Diario creado con ID ${id}`);
      return { success: true, data: diario };
    } catch (error) {
      return { success: false, error: { message: error.message } };
    }
  }

  async getDiarioById(id) {
    try {
      const diario = this.diarios.get(id);
      if (!diario) {
        throw new Error('Diario no encontrado');
      }

      console.log(`✅ Mock: Diario obtenido - ${diario.title}`);
      return { success: true, data: diario };
    } catch (error) {
      return { success: false, error: { message: error.message } };
    }
  }

  async createNewsProcessing(diarioId, status = 'pending') {
    try {
      const id = this.generateId();
      const processing = {
        id,
        diario_id: diarioId,
        status,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      this.processing.set(id, processing);

      console.log(`✅ Mock: Procesamiento creado para diario ${diarioId}`);
      return { success: true, data: processing };
    } catch (error) {
      return { success: false, error: { message: error.message } };
    }
  }

  async getProcessingByDiarioId(diarioId) {
    try {
      const processing = Array.from(this.processing.values())
        .find(p => p.diario_id === diarioId);

      if (!processing) {
        throw new Error('Procesamiento no encontrado');
      }

      return { success: true, data: processing };
    } catch (error) {
      return { success: false, error: { message: error.message } };
    }
  }

  async createMedio(medioData) {
    try {
      const id = this.generateId();
      const medio = {
        id,
        ...medioData,
        activo: medioData.activo !== undefined ? medioData.activo : true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      this.medios.set(id, medio);
      
      console.log(`✅ Mock: Medio creado - ${medio.nombre}`);
      return { success: true, data: medio };
    } catch (error) {
      return { success: false, error: { message: error.message } };
    }
  }
}

// Función para simular el flujo completo de webhook
async function testWebhookFlow() {
  console.log('🧪 Testing Flujo Completo de Webhook con Mock Database\n');
  
  const mockDB = new MockDatabaseService();
  
  // 1. Datos de diario de prueba
  const testDiario = {
    medio_origen: "test_medio_mock",
    header: "Titular de prueba con mock database",
    title: "Noticia de prueba: Testing con base de datos simulada",
    summary: "Esta es una noticia de prueba para validar el flujo completo con mock database.",
    content: "El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de procesamiento automatizado que utiliza inteligencia artificial para reescribir noticias según diferentes formatos de medios de comunicación.",
    images_url: ["https://ejemplo.com/imagen1.jpg"],
    categories: ["tecnología", "testing", "mock"]
  };

  try {
    console.log('📝 1. Creando diario en mock database...');
    const createResult = await mockDB.createDiario(testDiario);

    if (!createResult.success) {
      throw new Error(`Error creando diario: ${createResult.error.message}`);
    }

    const savedDiario = createResult.data;
    console.log(`   ✅ Diario guardado con ID: ${savedDiario.id}`);
    
    console.log('\n📝 2. Creando registro de procesamiento...');
    const processingResult = await mockDB.createNewsProcessing(savedDiario.id, 'pending');

    if (!processingResult.success) {
      throw new Error(`Error creando procesamiento: ${processingResult.error.message}`);
    }

    console.log(`   ✅ Procesamiento creado con ID: ${processingResult.data.id}`);

    console.log('\n📝 3. Consultando diario por ID...');
    const getResult = await mockDB.getDiarioById(savedDiario.id);

    if (!getResult.success) {
      throw new Error(`Error obteniendo diario: ${getResult.error.message}`);
    }

    console.log(`   ✅ Diario obtenido: ${getResult.data.title}`);

    console.log('\n📝 4. Consultando estado de procesamiento...');
    const statusResult = await mockDB.getProcessingByDiarioId(savedDiario.id);
    
    if (!statusResult.success) {
      throw new Error(`Error obteniendo estado: ${statusResult.error.message}`);
    }
    
    console.log(`   ✅ Estado de procesamiento: ${statusResult.data.status}`);
    
    console.log('\n📝 5. Simulando respuesta de webhook...');
    const webhookResponse = createApiResponse(
      true,
      'Diario recibido y guardado correctamente',
      {
        id: savedDiario.id,
        medio_origen: savedDiario.medio_origen,
        title: savedDiario.title,
        created_at: savedDiario.created_at,
        status: 'saved',
        processing_status: statusResult.data.status
      }
    );

    console.log('   ✅ Respuesta de webhook generada:');
    console.log(JSON.stringify(webhookResponse, null, 2));

    console.log('\n🎉 ¡Flujo completo de webhook simulado exitosamente!');
    console.log('\n📊 Resumen:');
    console.log(`   - Diario ID: ${savedDiario.id}`);
    console.log(`   - Medio: ${savedDiario.medio_origen}`);
    console.log(`   - Título: ${savedDiario.title}`);
    console.log(`   - Estado: ${statusResult.data.status}`);
    console.log(`   - Timestamp: ${savedDiario.created_at}`);
    
  } catch (error) {
    console.error('\n❌ Error en el flujo de testing:', error.message);
  }
}

// Función para testing de medios
async function testMediosFlow() {
  console.log('\n🧪 Testing Flujo de Medios con Mock Database\n');
  
  const mockDB = new MockDatabaseService();
  
  const testMedios = [
    {
      codigo: 'diario_test',
      nombre: 'Diario de Prueba',
      descripcion: 'Medio de comunicación para testing'
    },
    {
      codigo: 'tech_news_test',
      nombre: 'Tech News Test',
      descripcion: 'Medio especializado en tecnología para testing'
    }
  ];

  try {
    console.log('📝 Creando medios de prueba...');
    
    for (const medio of testMedios) {
      const result = await mockDB.createMedio(medio);
      if (result.success) {
        console.log(`   ✅ ${medio.nombre} - ID: ${result.data.id}`);
      } else {
        console.log(`   ❌ Error creando ${medio.nombre}: ${result.error.message}`);
      }
    }
    
    console.log('\n🎉 Medios creados exitosamente!');
    
  } catch (error) {
    console.error('\n❌ Error en el flujo de medios:', error.message);
  }
}

// Función principal
async function runMockTests() {
  console.log('🚀 Demo IA Server - Tests con Mock Database\n');
  console.log('=' * 60);
  
  await testWebhookFlow();
  await testMediosFlow();
  
  console.log('\n' + '=' * 60);
  console.log('🏁 Tests con mock database completados!');
  console.log('\n📋 Próximos pasos:');
  console.log('1. Configurar Supabase real siguiendo docs/SUPABASE_SETUP.md');
  console.log('2. Ejecutar node scripts/setup-database.js');
  console.log('3. Probar con base de datos real');
  console.log('4. Continuar con Fase 3 (OpenAI Integration)');
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  runMockTests();
}

module.exports = {
  MockDatabaseService,
  testWebhookFlow,
  testMediosFlow
};
