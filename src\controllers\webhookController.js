const { createApiResponse } = require('../models/newsSchema');
const { logger, logWebhookReceived, logError, logNewsProcessing } = require('../utils/logger');
const databaseService = require('../services/databaseService');
const processingService = require('../services/processingService');
const monitoringService = require('../services/monitoringService');

// Controlador principal para recibir webhooks de noticias
const receiveNewsWebhook = async (req, res) => {
  try {
    // Los datos ya están validados por el middleware
    const newsData = req.validatedData;

    // Log del webhook recibido
    logWebhookReceived(newsData);

    // 1. Guardar diario en Supabase
    logNewsProcessing('unknown', 'starting_database_save');

    const createResult = await databaseService.createDiario(newsData);

    if (!createResult.success) {
      logError(new Error('Error guardando diario en BD'), {
        context: 'receiveNewsWebhook',
        error: createResult.error,
        newsData: {
          medio_origen: newsData.medio_origen,
          title: newsData.title
        }
      });

      return res.status(500).json(
        createApiResponse(
          false,
          'Error guardando diario en base de datos',
          null,
          {
            code: 'DATABASE_ERROR',
            details: createResult.error.message
          }
        )
      );
    }

    const savedDiario = createResult.data;
    logNewsProcessing(savedDiario.id, 'database_save_completed');

    // 2. Crear registro de procesamiento
    const processingResult = await databaseService.createNewsProcessing(
      savedDiario.id,
      'pending'
    );

    if (!processingResult.success) {
      logError(new Error('Error creando registro de procesamiento'), {
        context: 'receiveNewsWebhook',
        diarioId: savedDiario.id,
        error: processingResult.error
      });
      // No fallar por esto, continuar
    }

    // 3. Log de éxito
    logger.info('Diario guardado y preparado para procesamiento', {
      id: savedDiario.id,
      medio_origen: savedDiario.medio_origen,
      title: savedDiario.title,
      content_length: savedDiario.content.length,
      has_images: savedDiario.images_url && savedDiario.images_url.length > 0,
      categories_count: savedDiario.categories ? savedDiario.categories.length : 0,
      processing_id: processingResult.success ? processingResult.data.id : null
    });

    // 3. Iniciar procesamiento automático con OpenAI (background)
    logNewsProcessing(savedDiario.id, 'starting_openai_processing');

    // Procesar en background sin bloquear la respuesta
    setImmediate(async () => {
      try {
        logger.info('Iniciando procesamiento automático en background', {
          diarioId: savedDiario.id,
          medio: savedDiario.medio_origen
        });

        const processingResult = await processingService.processNews(savedDiario, {
          background: true,
          priority: 'normal'
        });

        if (processingResult.success) {
          logger.info('Procesamiento automático completado', {
            diarioId: savedDiario.id,
            processingId: processingResult.data.processingId,
            tokensUsed: processingResult.data.processing.tokensUsed,
            processingTime: processingResult.data.processing.processingTime
          });
        } else {
          logger.error('Error en procesamiento automático', {
            diarioId: savedDiario.id,
            error: processingResult.error.message
          });
        }
      } catch (error) {
        logError(error, {
          context: 'background_processing',
          diarioId: savedDiario.id
        });
      }
    });

    // 4. Registrar webhook exitoso en monitoreo
    monitoringService.recordWebhook(true);

    // 5. Respuesta exitosa
    return res.status(200).json(
      createApiResponse(
        true,
        'Diario recibido y guardado correctamente',
        {
          id: savedDiario.id,
          medio_origen: savedDiario.medio_origen,
          title: savedDiario.title,
          created_at: savedDiario.created_at,
          status: 'saved',
          processing_status: processingResult.success ? processingResult.data.status : 'unknown',
          auto_processing: 'initiated'
        }
      )
    );

  } catch (error) {
    // Registrar error en monitoreo
    monitoringService.recordWebhook(false, error);

    logError(error, {
      context: 'receiveNewsWebhook',
      body: req.body,
      ip: req.ip
    });

    return res.status(500).json(
      createApiResponse(
        false,
        'Error interno del servidor',
        null,
        {
          code: 'INTERNAL_SERVER_ERROR',
          details: 'Error inesperado al procesar la noticia'
        }
      )
    );
  }
};

// Controlador para obtener el estado de una noticia
const getNewsStatus = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json(
        createApiResponse(
          false,
          'ID de noticia requerido',
          null,
          {
            code: 'MISSING_NEWS_ID',
            details: 'Se requiere el ID de la noticia en la URL'
          }
        )
      );
    }

    // 1. Obtener diario de la base de datos
    const diarioResult = await databaseService.getDiarioById(id);

    if (!diarioResult.success) {
      if (diarioResult.error.code === 'PGRST116') {
        return res.status(404).json(
          createApiResponse(
            false,
            'Diario no encontrado',
            null,
            {
              code: 'NEWS_NOT_FOUND',
              details: `No se encontró un diario con ID ${id}`
            }
          )
        );
      }

      return res.status(500).json(
        createApiResponse(
          false,
          'Error consultando diario',
          null,
          {
            code: 'DATABASE_ERROR',
            details: diarioResult.error.message
          }
        )
      );
    }

    const diario = diarioResult.data;

    // 2. Obtener estado de procesamiento
    const processingResult = await databaseService.getProcessingByDiarioId(id);

    let processingInfo = null;
    if (processingResult.success) {
      processingInfo = processingResult.data;
    }

    // 3. Construir respuesta con estado detallado
    const statusResponse = {
      id: diario.id,
      medio_origen: diario.medio_origen,
      title: diario.title,
      created_at: diario.created_at,
      updated_at: diario.updated_at,
      processing: processingInfo ? {
        status: processingInfo.status,
        created_at: processingInfo.created_at,
        updated_at: processingInfo.updated_at,
        completed_at: processingInfo.completed_at,
        openai_tokens_used: processingInfo.openai_tokens_used,
        processing_time_ms: processingInfo.processing_time_ms,
        error_message: processingInfo.error_message
      } : null,
      processing_steps: [
        {
          step: 'received',
          completed_at: diario.created_at,
          status: 'completed'
        },
        {
          step: 'validation',
          completed_at: diario.created_at,
          status: 'completed'
        },
        {
          step: 'database_storage',
          completed_at: diario.created_at,
          status: 'completed'
        },
        {
          step: 'openai_processing',
          status: processingInfo ?
            (processingInfo.status === 'completed' ? 'completed' :
             processingInfo.status === 'failed' ? 'failed' : 'pending') : 'pending',
          completed_at: processingInfo?.completed_at || null
        }
      ]
    };

    return res.status(200).json(
      createApiResponse(
        true,
        'Estado de diario obtenido',
        statusResponse
      )
    );

  } catch (error) {
    logError(error, {
      context: 'getNewsStatus',
      newsId: req.params.id
    });

    return res.status(500).json(
      createApiResponse(
        false,
        'Error al obtener estado de diario',
        null,
        {
          code: 'STATUS_RETRIEVAL_ERROR',
          details: 'Error inesperado al consultar el estado'
        }
      )
    );
  }
};

// Controlador para health check del webhook
const webhookHealthCheck = (req, res) => {
  try {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      webhook_endpoint: '/webhook/news',
      features: {
        validation: 'enabled',
        authentication: 'enabled',
        rate_limiting: 'enabled',
        logging: 'enabled'
      }
    };

    return res.status(200).json(
      createApiResponse(
        true,
        'Webhook service is healthy',
        healthData
      )
    );

  } catch (error) {
    logError(error, { context: 'webhookHealthCheck' });

    return res.status(500).json(
      createApiResponse(
        false,
        'Health check failed',
        null,
        {
          code: 'HEALTH_CHECK_ERROR',
          details: 'Error durante health check'
        }
      )
    );
  }
};

// Controlador para procesar noticia manualmente
const processNewsManually = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json(
        createApiResponse(
          false,
          'ID de noticia requerido',
          null,
          {
            code: 'MISSING_NEWS_ID',
            details: 'Se requiere el ID de la noticia en la URL'
          }
        )
      );
    }

    // 1. Obtener diario de la base de datos
    const diarioResult = await databaseService.getDiarioById(id);

    if (!diarioResult.success) {
      if (diarioResult.error.code === 'PGRST116') {
        return res.status(404).json(
          createApiResponse(
            false,
            'Diario no encontrado',
            null,
            {
              code: 'NEWS_NOT_FOUND',
              details: `No se encontró un diario con ID ${id}`
            }
          )
        );
      }

      return res.status(500).json(
        createApiResponse(
          false,
          'Error consultando diario',
          null,
          {
            code: 'DATABASE_ERROR',
            details: diarioResult.error.message
          }
        )
      );
    }

    const diario = diarioResult.data;

    // 2. Procesar con OpenAI
    logger.info('Iniciando procesamiento manual', {
      diarioId: id,
      medio: diario.medio_origen,
      requestedBy: req.ip
    });

    const processingResult = await processingService.processNews(diario, {
      manual: true,
      priority: 'high'
    });

    if (!processingResult.success) {
      return res.status(500).json(
        createApiResponse(
          false,
          'Error en procesamiento',
          null,
          {
            code: 'PROCESSING_ERROR',
            details: processingResult.error.message
          }
        )
      );
    }

    // 3. Respuesta exitosa con resultado
    return res.status(200).json(
      createApiResponse(
        true,
        'Noticia procesada exitosamente',
        {
          id: diario.id,
          medio_origen: diario.medio_origen,
          title: diario.title,
          processing: {
            id: processingResult.data.processingId,
            tokensUsed: processingResult.data.processing.tokensUsed,
            processingTime: processingResult.data.processing.processingTime,
            model: processingResult.data.processing.model
          },
          processedContent: processingResult.data.processedContent,
          prompt: {
            isCustom: processingResult.data.prompt.isCustom,
            medio: processingResult.data.prompt.medio
          }
        }
      )
    );

  } catch (error) {
    logError(error, {
      context: 'processNewsManually',
      newsId: req.params.id
    });

    return res.status(500).json(
      createApiResponse(
        false,
        'Error al procesar noticia',
        null,
        {
          code: 'MANUAL_PROCESSING_ERROR',
          details: 'Error inesperado durante el procesamiento'
        }
      )
    );
  }
};

// Controlador para procesar noticias pendientes en lote
const processPendingNews = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;

    logger.info('Iniciando procesamiento en lote', {
      limit,
      requestedBy: req.ip
    });

    const batchResult = await processingService.processPendingNews(limit);

    if (!batchResult.success) {
      return res.status(500).json(
        createApiResponse(
          false,
          'Error en procesamiento en lote',
          null,
          {
            code: 'BATCH_PROCESSING_ERROR',
            details: batchResult.error.message
          }
        )
      );
    }

    return res.status(200).json(
      createApiResponse(
        true,
        'Procesamiento en lote completado',
        {
          total: batchResult.data.total,
          success: batchResult.data.success,
          errors: batchResult.data.errors,
          stats: processingService.getStats()
        }
      )
    );

  } catch (error) {
    logError(error, {
      context: 'processPendingNews'
    });

    return res.status(500).json(
      createApiResponse(
        false,
        'Error en procesamiento en lote',
        null,
        {
          code: 'BATCH_ERROR',
          details: 'Error inesperado durante el procesamiento en lote'
        }
      )
    );
  }
};

// Controlador para obtener métricas del sistema
const getSystemMetrics = async (req, res) => {
  try {
    const metrics = monitoringService.getMetrics();
    const dbStats = await monitoringService.getDatabaseStats();
    const recentErrors = monitoringService.getRecentErrors(10);

    return res.status(200).json(
      createApiResponse(
        true,
        'Métricas del sistema obtenidas',
        {
          metrics,
          database: dbStats,
          recentErrors,
          processingService: processingService.getStats()
        }
      )
    );

  } catch (error) {
    logError(error, { context: 'getSystemMetrics' });

    return res.status(500).json(
      createApiResponse(
        false,
        'Error al obtener métricas',
        null,
        {
          code: 'METRICS_ERROR',
          details: 'Error inesperado al consultar métricas'
        }
      )
    );
  }
};

// Controlador para obtener reporte de salud
const getHealthReport = async (req, res) => {
  try {
    const healthReport = await monitoringService.getHealthReport();

    const statusCode = healthReport.status === 'healthy' ? 200 :
                      healthReport.status === 'warning' ? 200 : 503;

    return res.status(statusCode).json(
      createApiResponse(
        healthReport.status !== 'error',
        `Sistema ${healthReport.status}`,
        healthReport
      )
    );

  } catch (error) {
    logError(error, { context: 'getHealthReport' });

    return res.status(500).json(
      createApiResponse(
        false,
        'Error al generar reporte de salud',
        null,
        {
          code: 'HEALTH_REPORT_ERROR',
          details: 'Error inesperado al generar reporte'
        }
      )
    );
  }
};

// Controlador para obtener contenido procesado de una noticia
const getProcessedContent = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json(
        createApiResponse(
          false,
          'ID de noticia requerido',
          null,
          {
            code: 'MISSING_NEWS_ID',
            details: 'Se requiere el ID de la noticia en la URL'
          }
        )
      );
    }

    // 1. Obtener diario original
    const diarioResult = await databaseService.getDiarioById(id);

    if (!diarioResult.success) {
      if (diarioResult.error.code === 'PGRST116') {
        return res.status(404).json(
          createApiResponse(
            false,
            'Diario no encontrado',
            null,
            {
              code: 'NEWS_NOT_FOUND',
              details: `No se encontró un diario con ID ${id}`
            }
          )
        );
      }

      return res.status(500).json(
        createApiResponse(
          false,
          'Error consultando diario',
          null,
          {
            code: 'DATABASE_ERROR',
            details: diarioResult.error.message
          }
        )
      );
    }

    const diario = diarioResult.data;

    // 2. Obtener información de procesamiento
    const processingResult = await databaseService.getProcessingByDiarioId(id);

    if (!processingResult.success || !processingResult.data) {
      return res.status(404).json(
        createApiResponse(
          false,
          'Procesamiento no encontrado',
          null,
          {
            code: 'PROCESSING_NOT_FOUND',
            details: 'Esta noticia no ha sido procesada aún'
          }
        )
      );
    }

    const processing = processingResult.data;

    if (processing.status !== 'completed') {
      return res.status(202).json(
        createApiResponse(
          false,
          'Procesamiento no completado',
          {
            status: processing.status,
            message: processing.status === 'processing' ? 'Procesamiento en curso' :
                    processing.status === 'failed' ? 'Procesamiento falló' : 'Estado desconocido'
          },
          {
            code: 'PROCESSING_NOT_COMPLETED',
            details: `Estado actual: ${processing.status}`
          }
        )
      );
    }

    // 3. Construir respuesta con contenido procesado
    const response = {
      original: {
        id: diario.id,
        medio_origen: diario.medio_origen,
        title: diario.title,
        summary: diario.summary,
        content: diario.content,
        images_url: diario.images_url,
        categories: diario.categories,
        created_at: diario.created_at
      },
      processed: {
        // Aquí iría el contenido procesado cuando esté disponible
        // Por ahora simulamos la estructura
        content: "Contenido procesado no disponible en esta versión",
        processing_info: {
          status: processing.status,
          completed_at: processing.completed_at,
          tokens_used: processing.openai_tokens_used,
          processing_time_ms: processing.processing_time_ms,
          model_used: processing.model_used || 'gpt-3.5-turbo'
        }
      },
      comparison: {
        original_length: diario.content.length,
        processed_length: 0, // Se calculará cuando tengamos contenido procesado
        tokens_used: processing.openai_tokens_used || 0,
        processing_time: processing.processing_time_ms || 0
      }
    };

    return res.status(200).json(
      createApiResponse(
        true,
        'Contenido procesado obtenido',
        response
      )
    );

  } catch (error) {
    logError(error, {
      context: 'getProcessedContent',
      newsId: req.params.id
    });

    return res.status(500).json(
      createApiResponse(
        false,
        'Error al obtener contenido procesado',
        null,
        {
          code: 'PROCESSED_CONTENT_ERROR',
          details: 'Error inesperado al consultar el contenido procesado'
        }
      )
    );
  }
};

// Controlador para listar noticias recientes
const getRecentNews = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const offset = parseInt(req.query.offset) || 0;
    const medio = req.query.medio;

    logger.info('Consultando noticias recientes', {
      limit,
      offset,
      medio,
      requestedBy: req.ip
    });

    // Construir query
    let query = databaseService.client
      .from('diarios')
      .select(`
        id,
        medio_origen,
        title,
        summary,
        created_at,
        updated_at,
        news_processing!left (
          status,
          completed_at,
          openai_tokens_used
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Filtrar por medio si se especifica
    if (medio) {
      query = query.eq('medio_origen', medio);
    }

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    // Procesar datos para incluir estado de procesamiento
    const processedData = data.map(item => ({
      id: item.id,
      medio_origen: item.medio_origen,
      title: item.title,
      summary: item.summary,
      created_at: item.created_at,
      updated_at: item.updated_at,
      processing_status: item.news_processing?.[0]?.status || 'pending',
      tokens_used: item.news_processing?.[0]?.openai_tokens_used || 0,
      processed_at: item.news_processing?.[0]?.completed_at || null
    }));

    return res.status(200).json(
      createApiResponse(
        true,
        'Noticias recientes obtenidas',
        {
          news: processedData,
          pagination: {
            limit,
            offset,
            total: count,
            hasMore: (offset + limit) < count
          },
          filters: {
            medio: medio || 'all'
          }
        }
      )
    );

  } catch (error) {
    logError(error, {
      context: 'getRecentNews',
      query: req.query
    });

    return res.status(500).json(
      createApiResponse(
        false,
        'Error al obtener noticias recientes',
        null,
        {
          code: 'RECENT_NEWS_ERROR',
          details: 'Error inesperado al consultar noticias'
        }
      )
    );
  }
};

module.exports = {
  receiveNewsWebhook,
  getNewsStatus,
  webhookHealthCheck,
  processNewsManually,
  processPendingNews,
  getSystemMetrics,
  getHealthReport,
  getProcessedContent,
  getRecentNews
};
