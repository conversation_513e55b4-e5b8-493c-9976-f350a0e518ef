const { createApiResponse } = require('../models/newsSchema');
const { logger, logWebhookReceived, logError, logNewsProcessing } = require('../utils/logger');
const databaseService = require('../services/databaseService');

// Controlador principal para recibir webhooks de noticias
const receiveNewsWebhook = async (req, res) => {
  try {
    // Los datos ya están validados por el middleware
    const newsData = req.validatedData;

    // Log del webhook recibido
    logWebhookReceived(newsData);

    // 1. Guardar noticia en Supabase
    logNewsProcessing('unknown', 'starting_database_save');

    const createResult = await databaseService.createNoticia(newsData);

    if (!createResult.success) {
      logError(new Error('Error guardando noticia en BD'), {
        context: 'receiveNewsWebhook',
        error: createResult.error,
        newsData: {
          medio_origen: newsData.medio_origen,
          title: newsData.title
        }
      });

      return res.status(500).json(
        createApiResponse(
          false,
          'Error guardando noticia en base de datos',
          null,
          {
            code: 'DATABASE_ERROR',
            details: createResult.error.message
          }
        )
      );
    }

    const savedNoticia = createResult.data;
    logNewsProcessing(savedNoticia.id, 'database_save_completed');

    // 2. Crear registro de procesamiento
    const processingResult = await databaseService.createNewsProcessing(
      savedNoticia.id,
      'pending'
    );

    if (!processingResult.success) {
      logError(new Error('Error creando registro de procesamiento'), {
        context: 'receiveNewsWebhook',
        noticiaId: savedNoticia.id,
        error: processingResult.error
      });
      // No fallar por esto, continuar
    }

    // 3. Log de éxito
    logger.info('Noticia guardada y preparada para procesamiento', {
      id: savedNoticia.id,
      medio_origen: savedNoticia.medio_origen,
      title: savedNoticia.title,
      content_length: savedNoticia.content.length,
      has_images: savedNoticia.images_url && savedNoticia.images_url.length > 0,
      categories_count: savedNoticia.categories ? savedNoticia.categories.length : 0,
      processing_id: processingResult.success ? processingResult.data.id : null
    });

    // TODO: En la Fase 3 - Procesar con OpenAI

    // 4. Respuesta exitosa
    return res.status(200).json(
      createApiResponse(
        true,
        'Noticia recibida y guardada correctamente',
        {
          id: savedNoticia.id,
          medio_origen: savedNoticia.medio_origen,
          title: savedNoticia.title,
          created_at: savedNoticia.created_at,
          status: 'saved',
          processing_status: processingResult.success ? processingResult.data.status : 'unknown'
        }
      )
    );

  } catch (error) {
    logError(error, {
      context: 'receiveNewsWebhook',
      body: req.body,
      ip: req.ip
    });

    return res.status(500).json(
      createApiResponse(
        false,
        'Error interno del servidor',
        null,
        {
          code: 'INTERNAL_SERVER_ERROR',
          details: 'Error inesperado al procesar la noticia'
        }
      )
    );
  }
};

// Controlador para obtener el estado de una noticia
const getNewsStatus = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json(
        createApiResponse(
          false,
          'ID de noticia requerido',
          null,
          {
            code: 'MISSING_NEWS_ID',
            details: 'Se requiere el ID de la noticia en la URL'
          }
        )
      );
    }

    // 1. Obtener noticia de la base de datos
    const noticiaResult = await databaseService.getNoticiaById(id);

    if (!noticiaResult.success) {
      if (noticiaResult.error.code === 'PGRST116') {
        return res.status(404).json(
          createApiResponse(
            false,
            'Noticia no encontrada',
            null,
            {
              code: 'NEWS_NOT_FOUND',
              details: `No se encontró una noticia con ID ${id}`
            }
          )
        );
      }

      return res.status(500).json(
        createApiResponse(
          false,
          'Error consultando noticia',
          null,
          {
            code: 'DATABASE_ERROR',
            details: noticiaResult.error.message
          }
        )
      );
    }

    const noticia = noticiaResult.data;

    // 2. Obtener estado de procesamiento
    const processingResult = await databaseService.getProcessingByNoticiaId(id);

    let processingInfo = null;
    if (processingResult.success) {
      processingInfo = processingResult.data;
    }

    // 3. Construir respuesta con estado detallado
    const statusResponse = {
      id: noticia.id,
      medio_origen: noticia.medio_origen,
      title: noticia.title,
      created_at: noticia.created_at,
      updated_at: noticia.updated_at,
      processing: processingInfo ? {
        status: processingInfo.status,
        created_at: processingInfo.created_at,
        updated_at: processingInfo.updated_at,
        completed_at: processingInfo.completed_at,
        openai_tokens_used: processingInfo.openai_tokens_used,
        processing_time_ms: processingInfo.processing_time_ms,
        error_message: processingInfo.error_message
      } : null,
      processing_steps: [
        {
          step: 'received',
          completed_at: noticia.created_at,
          status: 'completed'
        },
        {
          step: 'validation',
          completed_at: noticia.created_at,
          status: 'completed'
        },
        {
          step: 'database_storage',
          completed_at: noticia.created_at,
          status: 'completed'
        },
        {
          step: 'openai_processing',
          status: processingInfo ?
            (processingInfo.status === 'completed' ? 'completed' :
             processingInfo.status === 'failed' ? 'failed' : 'pending') : 'pending',
          completed_at: processingInfo?.completed_at || null
        }
      ]
    };

    return res.status(200).json(
      createApiResponse(
        true,
        'Estado de noticia obtenido',
        statusResponse
      )
    );

  } catch (error) {
    logError(error, {
      context: 'getNewsStatus',
      newsId: req.params.id
    });

    return res.status(500).json(
      createApiResponse(
        false,
        'Error al obtener estado de noticia',
        null,
        {
          code: 'STATUS_RETRIEVAL_ERROR',
          details: 'Error inesperado al consultar el estado'
        }
      )
    );
  }
};

// Controlador para health check del webhook
const webhookHealthCheck = (req, res) => {
  try {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      webhook_endpoint: '/webhook/news',
      features: {
        validation: 'enabled',
        authentication: 'enabled',
        rate_limiting: 'enabled',
        logging: 'enabled'
      }
    };

    return res.status(200).json(
      createApiResponse(
        true,
        'Webhook service is healthy',
        healthData
      )
    );

  } catch (error) {
    logError(error, { context: 'webhookHealthCheck' });

    return res.status(500).json(
      createApiResponse(
        false,
        'Health check failed',
        null,
        {
          code: 'HEALTH_CHECK_ERROR',
          details: 'Error durante health check'
        }
      )
    );
  }
};

module.exports = {
  receiveNewsWebhook,
  getNewsStatus,
  webhookHealthCheck
};
