# Demo IA Server - Dockerfile
FROM node:18-alpine

# Metadatos
LABEL maintainer="Demo IA Server Team"
LABEL description="Sistema de procesamiento automatizado de noticias con IA"
LABEL version="1.0.0"

# Instalar dependencias del sistema
RUN apk add --no-cache \
    curl \
    dumb-init

# Crear directorio de trabajo
WORKDIR /app

# Crear usuario no-root para seguridad
RUN addgroup -g 1001 -S nodejs && \
    adduser -S demoai -u 1001 -G nodejs

# Copiar archivos de dependencias
COPY package*.json ./

# Instalar dependencias de producción
RUN npm ci --only=production && \
    npm cache clean --force

# Copiar código fuente
COPY --chown=demoai:nodejs . .

# Crear directorio de logs con permisos correctos
RUN mkdir -p logs && \
    chown -R demoai:nodejs logs && \
    chmod 755 logs

# Cambiar a usuario no-root
USER demoai

# Exponer puerto
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Usar dumb-init para manejo correcto de señales
ENTRYPOINT ["dumb-init", "--"]

# Comando de inicio
CMD ["npm", "start"]
