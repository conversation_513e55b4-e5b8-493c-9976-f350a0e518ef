const express = require('express');
const router = express.Router();
const { createApiResponse } = require('../models/newsSchema');
const config = require('../config/environment');

/**
 * GET /health
 * Health check general del sistema
 */
router.get('/health', (req, res) => {
  try {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: config.server.nodeEnv,
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024)
      },
      services: {
        webhook: 'enabled',
        validation: 'enabled',
        authentication: 'enabled',
        rate_limiting: 'enabled',
        logging: 'enabled'
      },
      endpoints: {
        webhook_news: '/webhook/news',
        webhook_status: '/webhook/news/:id/status',
        health: '/health',
        ping: '/ping'
      }
    };

    res.status(200).json(
      createApiResponse(
        true,
        'Sistema funcionando correctamente',
        healthData
      )
    );

  } catch (error) {
    res.status(500).json(
      createApiResponse(
        false,
        'Error en health check',
        null,
        {
          code: 'HEALTH_CHECK_ERROR',
          details: error.message
        }
      )
    );
  }
});

/**
 * GET /ping
 * Endpoint simple para verificar conectividad
 */
router.get('/ping', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'pong',
    timestamp: new Date().toISOString(),
    server: 'demo-ia-server'
  });
});

/**
 * GET /ready
 * Readiness check para verificar que el servicio está listo para recibir tráfico
 */
router.get('/ready', (req, res) => {
  try {
    // Verificar configuración crítica
    const isReady = config.webhook.secret && 
                   config.supabase.url && 
                   config.openai.apiKey;

    if (isReady) {
      res.status(200).json(
        createApiResponse(
          true,
          'Servicio listo para recibir tráfico',
          {
            ready: true,
            timestamp: new Date().toISOString(),
            checks: {
              webhook_secret: !!config.webhook.secret,
              supabase_config: !!config.supabase.url,
              openai_config: !!config.openai.apiKey
            }
          }
        )
      );
    } else {
      res.status(503).json(
        createApiResponse(
          false,
          'Servicio no está listo',
          {
            ready: false,
            timestamp: new Date().toISOString(),
            checks: {
              webhook_secret: !!config.webhook.secret,
              supabase_config: !!config.supabase.url,
              openai_config: !!config.openai.apiKey
            }
          }
        )
      );
    }

  } catch (error) {
    res.status(500).json(
      createApiResponse(
        false,
        'Error en readiness check',
        null,
        {
          code: 'READINESS_CHECK_ERROR',
          details: error.message
        }
      )
    );
  }
});

module.exports = router;
