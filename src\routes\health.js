const express = require('express');
const router = express.Router();
const { createApiResponse } = require('../models/newsSchema');
const config = require('../config/environment');
const { checkTablesExist, testSupabaseConnection } = require('../config/database');

/**
 * GET /health
 * Health check general del sistema
 */
router.get('/health', async (req, res) => {
  try {
    // Verificar estado de la base de datos
    const dbConnectionOk = await testSupabaseConnection();
    const tablesStatus = await checkTablesExist();

    const healthData = {
      status: dbConnectionOk && tablesStatus.allTablesExist ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: config.server.nodeEnv,
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024)
      },
      services: {
        webhook: 'enabled',
        validation: 'enabled',
        authentication: 'enabled',
        rate_limiting: 'enabled',
        logging: 'enabled',
        database: dbConnectionOk ? 'connected' : 'disconnected',
        supabase: dbConnectionOk ? 'healthy' : 'unhealthy'
      },
      database: {
        connection: dbConnectionOk ? 'ok' : 'failed',
        tables: {
          total: 5,
          existing: tablesStatus.existingTables.length,
          missing: tablesStatus.missingTables.length,
          all_present: tablesStatus.allTablesExist
        }
      },
      endpoints: {
        webhook_news: '/webhook/news',
        webhook_status: '/webhook/news/:id/status',
        health: '/health',
        ping: '/ping',
        ready: '/ready'
      }
    };

    const statusCode = healthData.status === 'healthy' ? 200 : 503;

    res.status(statusCode).json(
      createApiResponse(
        healthData.status === 'healthy',
        healthData.status === 'healthy' ?
          'Sistema funcionando correctamente' :
          'Sistema con problemas - verificar base de datos',
        healthData
      )
    );

  } catch (error) {
    res.status(500).json(
      createApiResponse(
        false,
        'Error en health check',
        null,
        {
          code: 'HEALTH_CHECK_ERROR',
          details: error.message
        }
      )
    );
  }
});

/**
 * GET /ping
 * Endpoint simple para verificar conectividad
 */
router.get('/ping', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'pong',
    timestamp: new Date().toISOString(),
    server: 'demo-ia-server'
  });
});

/**
 * GET /ready
 * Readiness check para verificar que el servicio está listo para recibir tráfico
 */
router.get('/ready', async (req, res) => {
  try {
    // Verificar configuración crítica
    const configChecks = {
      webhook_secret: !!config.webhook.secret,
      supabase_config: !!config.supabase.url && !!config.supabase.serviceRoleKey,
      openai_config: !!config.openai.apiKey
    };

    // Verificar conexión a base de datos
    const dbConnectionOk = await testSupabaseConnection();
    const tablesStatus = await checkTablesExist();

    const allChecks = {
      ...configChecks,
      database_connection: dbConnectionOk,
      database_tables: tablesStatus.allTablesExist
    };

    const isReady = Object.values(allChecks).every(check => check === true);

    if (isReady) {
      res.status(200).json(
        createApiResponse(
          true,
          'Servicio listo para recibir tráfico',
          {
            ready: true,
            timestamp: new Date().toISOString(),
            checks: allChecks,
            database: {
              tables_existing: tablesStatus.existingTables.length,
              tables_total: 5,
              missing_tables: tablesStatus.missingTables
            }
          }
        )
      );
    } else {
      res.status(503).json(
        createApiResponse(
          false,
          'Servicio no está listo',
          {
            ready: false,
            timestamp: new Date().toISOString(),
            checks: allChecks,
            database: {
              tables_existing: tablesStatus.existingTables.length,
              tables_total: 5,
              missing_tables: tablesStatus.missingTables
            },
            issues: Object.entries(allChecks)
              .filter(([key, value]) => !value)
              .map(([key]) => key)
          }
        )
      );
    }

  } catch (error) {
    res.status(500).json(
      createApiResponse(
        false,
        'Error en readiness check',
        null,
        {
          code: 'READINESS_CHECK_ERROR',
          details: error.message
        }
      )
    );
  }
});

module.exports = router;
