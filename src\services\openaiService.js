// Servicio de integración con OpenAI API
const OpenAI = require('openai');
const config = require('../config/environment');
const { logger, logError } = require('../utils/logger');

class OpenAIService {
  constructor() {
    this.client = null;
    this.isInitialized = false;
    this.rateLimitInfo = {
      requestsPerMinute: 0,
      tokensPerMinute: 0,
      lastReset: Date.now()
    };
  }

  // Inicializar cliente de OpenAI
  initialize() {
    try {
      if (!config.openai.apiKey || config.openai.apiKey === 'temp_openai_key') {
        logger.warn('OpenAI API key no configurada o es temporal', {
          hasKey: !!config.openai.apiKey,
          isTemp: config.openai.apiKey === 'temp_openai_key'
        });
        return false;
      }

      this.client = new OpenAI({
        apiKey: config.openai.apiKey,
        timeout: 30000, // 30 segundos
        maxRetries: 3
      });

      this.isInitialized = true;
      logger.info('Cliente OpenAI inicializado correctamente', {
        model: config.openai.model,
        maxTokens: config.openai.maxTokens
      });

      return true;
    } catch (error) {
      logError(error, { context: 'OpenAIService.initialize' });
      return false;
    }
  }

  // Verificar si el servicio está disponible
  isAvailable() {
    return this.isInitialized && this.client !== null;
  }

  // Actualizar información de rate limiting
  updateRateLimitInfo(headers) {
    const now = Date.now();
    
    // Reset counters every minute
    if (now - this.rateLimitInfo.lastReset > 60000) {
      this.rateLimitInfo.requestsPerMinute = 0;
      this.rateLimitInfo.tokensPerMinute = 0;
      this.rateLimitInfo.lastReset = now;
    }

    // Update from headers if available
    if (headers) {
      this.rateLimitInfo.requestsRemaining = headers['x-ratelimit-remaining-requests'];
      this.rateLimitInfo.tokensRemaining = headers['x-ratelimit-remaining-tokens'];
      this.rateLimitInfo.resetTime = headers['x-ratelimit-reset-requests'];
    }

    this.rateLimitInfo.requestsPerMinute++;
  }

  // Procesar noticia con OpenAI
  async processNews(newsData, promptTemplate, medioConfig = {}) {
    try {
      if (!this.isAvailable()) {
        throw new Error('OpenAI service no está disponible');
      }

      const startTime = Date.now();

      logger.info('Iniciando procesamiento con OpenAI', {
        newsId: newsData.id,
        medio: newsData.medio_origen,
        titleLength: newsData.title.length,
        contentLength: newsData.content.length
      });

      // Construir prompt personalizado
      const prompt = this.buildPrompt(newsData, promptTemplate, medioConfig);
      
      logger.info('Prompt construido', {
        promptLength: prompt.length,
        newsId: newsData.id
      });

      // Realizar llamada a OpenAI
      const response = await this.client.chat.completions.create({
        model: config.openai.model,
        messages: [
          {
            role: 'system',
            content: 'Eres un experto periodista y editor que reescribe noticias adaptándolas al estilo y formato específico de diferentes medios de comunicación.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: config.openai.maxTokens,
        temperature: 0.7,
        top_p: 0.9,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      });

      const processingTime = Date.now() - startTime;

      // Actualizar rate limiting
      this.updateRateLimitInfo(response.headers);

      // Extraer resultado
      const processedContent = response.choices[0]?.message?.content;
      
      if (!processedContent) {
        throw new Error('OpenAI no devolvió contenido procesado');
      }

      // Calcular tokens utilizados
      const tokensUsed = response.usage?.total_tokens || 0;
      this.rateLimitInfo.tokensPerMinute += tokensUsed;

      logger.info('Procesamiento OpenAI completado', {
        newsId: newsData.id,
        tokensUsed,
        processingTime,
        outputLength: processedContent.length,
        model: response.model
      });

      return {
        success: true,
        data: {
          processedContent,
          originalNews: newsData,
          processing: {
            tokensUsed,
            processingTime,
            model: response.model,
            timestamp: new Date().toISOString()
          },
          usage: response.usage
        }
      };

    } catch (error) {
      logError(error, {
        context: 'OpenAIService.processNews',
        newsId: newsData.id,
        medio: newsData.medio_origen
      });

      return {
        success: false,
        error: {
          code: 'OPENAI_PROCESSING_ERROR',
          message: error.message,
          type: error.type || 'unknown',
          originalError: error
        }
      };
    }
  }

  // Construir prompt personalizado
  buildPrompt(newsData, template, medioConfig) {
    try {
      // Template base si no se proporciona uno específico
      const defaultTemplate = `
Reescribe la siguiente noticia adaptándola al estilo del medio "${newsData.medio_origen}":

NOTICIA ORIGINAL:
Título: {title}
Resumen: {summary}
Contenido: {content}

INSTRUCCIONES:
- Mantén la información factual exacta
- Adapta el tono y estilo al medio especificado
- Conserva la estructura: título, resumen, contenido
- Máximo {maxWords} palabras en el contenido

FORMATO DE RESPUESTA:
TÍTULO: [nuevo título adaptado]
RESUMEN: [nuevo resumen adaptado]
CONTENIDO: [nuevo contenido adaptado]
      `.trim();

      const promptTemplate = template || defaultTemplate;

      // Reemplazar variables en el template
      let prompt = promptTemplate
        .replace(/{title}/g, newsData.title)
        .replace(/{summary}/g, newsData.summary || '')
        .replace(/{content}/g, newsData.content)
        .replace(/{medio}/g, newsData.medio_origen)
        .replace(/{maxWords}/g, medioConfig.maxWords || '500');

      // Agregar configuraciones específicas del medio si existen
      if (medioConfig.style) {
        prompt += `\n\nESTILO ESPECÍFICO: ${medioConfig.style}`;
      }

      if (medioConfig.tone) {
        prompt += `\nTONO: ${medioConfig.tone}`;
      }

      if (medioConfig.audience) {
        prompt += `\nAUDIENCIA: ${medioConfig.audience}`;
      }

      return prompt;

    } catch (error) {
      logError(error, { context: 'OpenAIService.buildPrompt' });
      throw new Error('Error construyendo prompt para OpenAI');
    }
  }

  // Obtener información de rate limiting
  getRateLimitInfo() {
    return {
      ...this.rateLimitInfo,
      isAvailable: this.isAvailable(),
      lastUpdate: new Date().toISOString()
    };
  }

  // Test de conectividad con OpenAI
  async testConnection() {
    try {
      if (!this.isAvailable()) {
        return {
          success: false,
          error: 'OpenAI service no inicializado'
        };
      }

      const response = await this.client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: 'Responde solo con "OK" si puedes procesar este mensaje.'
          }
        ],
        max_tokens: 10
      });

      const result = response.choices[0]?.message?.content?.trim();

      return {
        success: true,
        data: {
          response: result,
          model: response.model,
          tokensUsed: response.usage?.total_tokens || 0
        }
      };

    } catch (error) {
      return {
        success: false,
        error: {
          message: error.message,
          type: error.type,
          code: error.code
        }
      };
    }
  }
}

// Exportar instancia singleton
const openaiService = new OpenAIService();

module.exports = openaiService;
