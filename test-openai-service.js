// Test del servicio OpenAI
const openaiService = require('./src/services/openaiService');
const config = require('./src/config/environment');

async function testOpenAIService() {
  console.log('🤖 Test del Servicio OpenAI\n');
  
  try {
    console.log('1. 🔍 Verificando configuración...');
    console.log('   API Key:', config.openai.apiKey ? 
      config.openai.apiKey.substring(0, 10) + '...' : 'NO CONFIGURADA');
    console.log('   Modelo:', config.openai.model);
    console.log('   Max Tokens:', config.openai.maxTokens);
    console.log('   Temperature:', config.openai.temperature);
    
    console.log('\n2. 🔧 Inicializando servicio...');
    const initialized = openaiService.initialize();
    
    if (!initialized) {
      console.log('   ❌ No se pudo inicializar OpenAI service');
      console.log('   ⚠️  Esto es normal si no tienes una API key real configurada');
      console.log('   💡 Para probar con API key real, actualiza OPENAI_API_KEY en .env');
      return;
    }
    
    console.log('   ✅ Servicio inicializado correctamente');
    console.log('   📊 Disponible:', openaiService.isAvailable());
    
    console.log('\n3. 🔍 Test de conectividad...');
    const connectionTest = await openaiService.testConnection();
    
    if (!connectionTest.success) {
      console.log('   ❌ Test de conectividad falló:', connectionTest.error.message);
      console.log('   💡 Verifica tu API key de OpenAI');
      return;
    }
    
    console.log('   ✅ Conectividad exitosa');
    console.log('   📝 Respuesta:', connectionTest.data.response);
    console.log('   🔢 Tokens usados:', connectionTest.data.tokensUsed);
    console.log('   🤖 Modelo:', connectionTest.data.model);
    
    console.log('\n4. 🧪 Test de procesamiento de noticia...');
    
    // Noticia de prueba
    const testNews = {
      id: 'test-news-001',
      medio_origen: 'test_medio',
      title: 'Avances en Inteligencia Artificial Revolucionan la Industria',
      summary: 'Nuevos desarrollos en IA están transformando múltiples sectores.',
      content: 'Los recientes avances en inteligencia artificial están revolucionando la forma en que las empresas operan. Desde el procesamiento de lenguaje natural hasta el aprendizaje automático, estas tecnologías están siendo adoptadas en sectores como la salud, finanzas y educación. Los expertos predicen que esta tendencia continuará acelerándose en los próximos años, creando nuevas oportunidades y desafíos para las organizaciones.'
    };
    
    console.log('   📰 Noticia de prueba:', {
      id: testNews.id,
      medio: testNews.medio_origen,
      titleLength: testNews.title.length,
      contentLength: testNews.content.length
    });
    
    // Configuración del medio de prueba
    const medioConfig = {
      style: 'formal y profesional',
      tone: 'informativo',
      audience: 'profesionales de tecnología',
      maxWords: '300'
    };
    
    console.log('   ⚙️  Configuración del medio:', medioConfig);
    
    const startTime = Date.now();
    const result = await openaiService.processNews(testNews, null, medioConfig);
    const processingTime = Date.now() - startTime;
    
    if (!result.success) {
      console.log('   ❌ Procesamiento falló:', result.error.message);
      return;
    }
    
    console.log('   ✅ Procesamiento exitoso!');
    console.log('   ⏱️  Tiempo total:', processingTime, 'ms');
    console.log('   🔢 Tokens usados:', result.data.processing.tokensUsed);
    console.log('   🤖 Modelo usado:', result.data.processing.model);
    console.log('   📏 Longitud resultado:', result.data.processedContent.length, 'caracteres');
    
    console.log('\n   📄 RESULTADO PROCESADO:');
    console.log('   ' + '='.repeat(50));
    console.log(result.data.processedContent);
    console.log('   ' + '='.repeat(50));
    
    console.log('\n5. 📊 Información de Rate Limiting...');
    const rateLimitInfo = openaiService.getRateLimitInfo();
    console.log('   📈 Requests por minuto:', rateLimitInfo.requestsPerMinute);
    console.log('   🔢 Tokens por minuto:', rateLimitInfo.tokensPerMinute);
    console.log('   ⏰ Último reset:', new Date(rateLimitInfo.lastReset).toLocaleTimeString());
    
    if (rateLimitInfo.requestsRemaining) {
      console.log('   🔄 Requests restantes:', rateLimitInfo.requestsRemaining);
    }
    
    if (rateLimitInfo.tokensRemaining) {
      console.log('   🎯 Tokens restantes:', rateLimitInfo.tokensRemaining);
    }
    
    console.log('\n✅ Test del servicio OpenAI completado exitosamente!');
    
  } catch (error) {
    console.error('\n💥 Error en test:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Función para test con API key temporal (mock)
async function testWithMockKey() {
  console.log('🎭 Test con API Key Mock (sin llamadas reales)\n');
  
  console.log('1. 🔍 Verificando configuración mock...');
  console.log('   API Key:', config.openai.apiKey);
  console.log('   Es temporal:', config.openai.apiKey === 'temp_openai_key');
  
  console.log('\n2. 🔧 Intentando inicializar...');
  const initialized = openaiService.initialize();
  
  if (initialized) {
    console.log('   ⚠️  Servicio inicializado con key temporal');
  } else {
    console.log('   ✅ Servicio correctamente rechazó key temporal');
  }
  
  console.log('\n3. 📊 Estado del servicio...');
  console.log('   Disponible:', openaiService.isAvailable());
  console.log('   Rate limit info:', openaiService.getRateLimitInfo());
  
  console.log('\n💡 Para probar con API key real:');
  console.log('   1. Obtén una API key de OpenAI');
  console.log('   2. Actualiza OPENAI_API_KEY en .env');
  console.log('   3. Ejecuta: node test-openai-service.js');
}

// Ejecutar test apropiado
if (require.main === module) {
  if (config.openai.apiKey === 'temp_openai_key' || !config.openai.apiKey) {
    testWithMockKey();
  } else {
    testOpenAIService();
  }
}

module.exports = { testOpenAIService, testWithMockKey };
