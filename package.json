{"name": "demo-ia-server", "version": "1.0.0", "description": "Sistema backend para procesamiento automatizado de noticias con OpenAI y Supabase", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["news", "openai", "supabase", "webhook", "automation"], "author": "Tu Nombre", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.38.0", "axios": "^1.10.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "openai": "^4.104.0", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.5", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}