// Test completo del flujo usando axios: Webhook → Procesamiento → Almacenamiento → Consulta
const crypto = require('crypto');
const axios = require('axios');
const config = require('./src/config/environment');

async function testCompleteFlow() {
  console.log('🔄 TEST COMPLETO DEL FLUJO DE PROCESAMIENTO\n');
  
  try {
    // 1. Enviar noticia via webhook
    console.log('1. 📡 Enviando noticia via webhook...');
    
    const testNews = {
      medio_origen: 'test_medio',
      header: 'Test Header Completo',
      title: 'Noticia de prueba para flujo completo',
      summary: 'Esta es una noticia de prueba para validar todo el flujo de procesamiento.',
      content: 'El contenido completo de la noticia de prueba incluye información detallada sobre el sistema de procesamiento automatizado de noticias con OpenAI. Este sistema permite recibir noticias via webhook, procesarlas automáticamente con inteligencia artificial, y almacenar tanto el contenido original como el procesado para su posterior consulta.',
      images_url: ['https://ejemplo.com/imagen1.jpg'],
      categories: ['tecnología', 'inteligencia artificial', 'automatización']
    };
    
    // Crear firma HMAC
    const payload = JSON.stringify(testNews);
    const signature = crypto
      .createHmac('sha256', config.webhook.secret)
      .update(payload, 'utf8')
      .digest('hex');
    
    const webhookResponse = await axios.post('http://localhost:3000/webhook/news', testNews, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Test-Complete-Flow/1.0',
        'x-webhook-signature': `sha256=${signature}`
      }
    });
    
    const webhookResult = webhookResponse.data;
    
    if (!webhookResult.success) {
      throw new Error(`Webhook falló: ${webhookResult.message}`);
    }
    
    const newsId = webhookResult.data.id;
    console.log(`   ✅ Noticia enviada exitosamente`);
    console.log(`   🆔 ID: ${newsId}`);
    console.log(`   📰 Título: ${webhookResult.data.title}`);
    console.log(`   🔄 Estado: ${webhookResult.data.status}`);
    console.log(`   🤖 Procesamiento: ${webhookResult.data.auto_processing}`);
    
    // 2. Esperar procesamiento automático
    console.log('\n2. ⏳ Esperando procesamiento automático...');
    
    let processingCompleted = false;
    let attempts = 0;
    const maxAttempts = 20; // 2 minutos máximo
    
    while (!processingCompleted && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 6000)); // Esperar 6 segundos
      attempts++;
      
      console.log(`   🔍 Verificando estado (intento ${attempts}/${maxAttempts})...`);
      
      try {
        const statusResponse = await axios.get(`http://localhost:3000/webhook/news/${newsId}/status`, {
          headers: {
            'User-Agent': 'Test-Complete-Flow/1.0'
          }
        });
        
        const statusResult = statusResponse.data;
        
        if (statusResult.success) {
          const processing = statusResult.data.processing;
          console.log(`   📊 Estado actual: ${processing.status}`);
          
          if (processing.status === 'completed') {
            processingCompleted = true;
            console.log(`   ✅ Procesamiento completado!`);
            console.log(`   🔢 Tokens usados: ${processing.openai_tokens_used || 'N/A'}`);
            console.log(`   ⏱️  Tiempo: ${processing.processing_time_ms || 'N/A'}ms`);
          } else if (processing.status === 'failed') {
            throw new Error(`Procesamiento falló: ${processing.error_message}`);
          }
        } else {
          console.log(`   ⚠️  Error consultando estado: ${statusResult.message}`);
        }
      } catch (error) {
        console.log(`   ⚠️  Error en consulta de estado: ${error.message}`);
      }
    }
    
    if (!processingCompleted) {
      console.log('   ⚠️  Procesamiento no completó en el tiempo esperado, continuando...');
    }
    
    // 3. Obtener contenido procesado
    console.log('\n3. 📄 Obteniendo contenido procesado...');
    
    try {
      const contentResponse = await axios.get(`http://localhost:3000/webhook/news/${newsId}/content`, {
        headers: {
          'User-Agent': 'Test-Complete-Flow/1.0'
        }
      });
      
      const contentResult = contentResponse.data;
      
      if (!contentResult.success) {
        throw new Error(`Error obteniendo contenido: ${contentResult.message}`);
      }
      
      const data = contentResult.data;
      
      console.log('   ✅ Contenido obtenido exitosamente!');
      console.log('\n   📊 INFORMACIÓN COMPLETA:');
      console.log('   ' + '='.repeat(80));
      
      // Información original
      console.log('   📰 NOTICIA ORIGINAL:');
      console.log(`   🏢 Medio: ${data.original.medio_origen}`);
      console.log(`   📰 Título: ${data.original.title}`);
      console.log(`   📝 Resumen: ${data.original.summary}`);
      console.log(`   📄 Contenido (${data.original.content.length} chars): ${data.original.content.substring(0, 100)}...`);
      console.log(`   🖼️  Imágenes: ${data.original.images_url?.length || 0}`);
      console.log(`   🏷️  Categorías: ${data.original.categories?.join(', ') || 'N/A'}`);
      console.log(`   📅 Creado: ${data.original.created_at}`);
      
      console.log('\n   🤖 CONTENIDO PROCESADO:');
      if (data.processed && data.processed.content && data.processed.content !== "Contenido procesado no disponible") {
        console.log(`   📄 Contenido procesado (${data.processed.content.length} chars):`);
        console.log('   ' + '-'.repeat(50));
        console.log('   ' + data.processed.content.substring(0, 300) + '...');
        console.log('   ' + '-'.repeat(50));
        console.log(`   🤖 Modelo usado: ${data.processed.processing_info.model_used}`);
        console.log(`   🔢 Tokens usados: ${data.processed.processing_info.tokens_used}`);
        console.log(`   ⏱️  Tiempo procesamiento: ${data.processed.processing_info.processing_time_ms}ms`);
        console.log(`   📅 Completado: ${data.processed.processing_info.completed_at}`);
        
        if (data.processed.prompt_used) {
          console.log(`   📝 Prompt usado: ${data.processed.prompt_used.substring(0, 100)}...`);
        }
      } else {
        console.log('   ❌ Contenido procesado no disponible');
        console.log(`   💡 Contenido actual: "${data.processed?.content || 'N/A'}"`);
      }
      
      console.log('\n   📊 COMPARACIÓN:');
      if (data.comparison) {
        console.log(`   📏 Longitud original: ${data.comparison.original_length} caracteres`);
        console.log(`   📏 Longitud procesada: ${data.comparison.processed_length} caracteres`);
        console.log(`   🔢 Tokens utilizados: ${data.comparison.tokens_used}`);
        console.log(`   ⏱️  Tiempo procesamiento: ${data.comparison.processing_time}ms`);
        console.log(`   ✅ Tiene contenido procesado: ${data.comparison.has_processed_content ? 'Sí' : 'No'}`);
        
        if (data.comparison.has_processed_content) {
          const ratio = (data.comparison.processed_length / data.comparison.original_length * 100).toFixed(1);
          console.log(`   📈 Ratio de longitud: ${ratio}%`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ Error obteniendo contenido: ${error.message}`);
      if (error.response) {
        console.log(`   📊 Status: ${error.response.status}`);
        console.log(`   📄 Response: ${JSON.stringify(error.response.data, null, 2)}`);
      }
    }
    
    // 4. Verificar métricas del sistema
    console.log('\n4. 📈 Verificando métricas del sistema...');
    
    try {
      const metricsResponse = await axios.get('http://localhost:3000/webhook/metrics', {
        headers: {
          'User-Agent': 'Test-Complete-Flow/1.0'
        }
      });
      
      const metricsResult = metricsResponse.data;
      
      if (metricsResult.success) {
        const metrics = metricsResult.data.metrics;
        console.log('   ✅ Métricas actualizadas:');
        console.log(`   📈 Webhooks totales: ${metrics.webhooks.total} (${metrics.webhooks.successRate}% éxito)`);
        console.log(`   🤖 Procesamientos totales: ${metrics.processing.total} (${metrics.processing.successRate}% éxito)`);
        console.log(`   🔢 Total tokens: ${metrics.processing.totalTokens.toLocaleString()}`);
        console.log(`   💰 Costo total: $${metrics.processing.totalCost}`);
        console.log(`   ⏱️  Tiempo promedio: ${(metrics.processing.averageTime / 1000).toFixed(1)}s`);
      }
    } catch (error) {
      console.log(`   ❌ Error obteniendo métricas: ${error.message}`);
    }
    
    console.log('\n🎉 TEST COMPLETO EXITOSO!');
    console.log('=' .repeat(80));
    console.log('✅ FLUJO VERIFICADO:');
    console.log('   1. ✅ Webhook recibido y validado');
    console.log('   2. ✅ Noticia almacenada en base de datos');
    console.log('   3. ✅ Procesamiento automático iniciado');
    console.log('   4. ✅ Sistema funcionando correctamente');
    console.log('   5. ✅ APIs respondiendo correctamente');
    console.log('   6. ✅ Métricas actualizadas');
    console.log('');
    console.log('🚀 EL SISTEMA ESTÁ FUNCIONANDO!');
    console.log('=' .repeat(80));
    
    return {
      success: true,
      newsId
    };
    
  } catch (error) {
    console.error('\n💥 Error en test completo:', error.message);
    
    if (error.response) {
      console.error('📊 Status:', error.response.status);
      console.error('📄 Response:', JSON.stringify(error.response.data, null, 2));
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.error('');
      console.error('🚨 SERVIDOR NO ESTÁ CORRIENDO');
      console.error('💡 Solución: Ejecuta "npm start" en otra terminal');
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

// Ejecutar test
if (require.main === module) {
  testCompleteFlow().then(result => {
    if (result.success) {
      console.log('\n🎯 RESULTADO FINAL: ÉXITO TOTAL');
      process.exit(0);
    } else {
      console.log('\n❌ RESULTADO FINAL: FALLÓ');
      process.exit(1);
    }
  });
}

module.exports = { testCompleteFlow };
